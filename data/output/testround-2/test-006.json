{"original_claim": ["Das Instandsetzungskonzept schreibt vor, dass sämtliche Massnahmen mithil<PERSON> von Augmented-Reality-Brillen dokumentiert werden."], "extraction": {"original_claim": ["Das Instandsetzungskonzept schreibt vor, dass sämtliche Massnahmen mithil<PERSON> von Augmented-Reality-Brillen dokumentiert werden."], "extraction_prompt_request": "You are a precise fact extraction system. Your task is to extract atomic facts from a given text.\n\nINSTRUCTIONS:\n- Break down the input text into individual, atomic facts\n- Each fact should be a simple, standalone statement\n- Preserve the original meaning and context\n- Do not add any information not present in the original text\n- Do not make assumptions or inferences beyond what is explicitly stated\n- Each fact should be a complete sentence\n- Avoid redundancy and repetition\n- Extract only factual statements, not opinions or subjective claims\n- Return each fact as a triple in the format (subject, predicate, object)\n- Remove articles (\"the\", \"a\", \"an\", \"das\", \"die\", \"der\", etc.) from the beginning of subjects and objects\n- Keep the core entity names without articles to facilitate entity matching\n- Ensure entity names are clean and directly matchable with knowledge graph entries\n- Always keep the language of the triples in German. Do not translate or change the language of the input or output; the triples must remain in German.\n\nINPUT:\n['Das Instandsetzungskonzept schreibt vor, dass sämtliche Massnahmen mithilfe von Augmented-Reality-Brillen dokumentiert werden.']\n\nOUTPUT:\nList each atomic fact as a triple (subject, predicate, object) on a new line. Remove articles from the beginning of subjects and objects.", "extraction_prompt_result": "(Instandsetzung<PERSON><PERSON><PERSON>, schre<PERSON><PERSON> vor, dass sämtliche Massnahmen mithil<PERSON> von Augmented-Reality-B<PERSON>n dokumentiert werden)", "filtering_prompt_request": "", "filtering_prompt_result": "", "facts": [{"subject": "Instandsetzungskonzept", "predicate": "schre<PERSON>t vor", "object": "dass sämtliche Massnahmen mithilfe von Augmented-Reality-B<PERSON>n dokumentiert werden"}], "entities": ["dass sämtliche Massnahmen mithilfe von Augmented-Reality-B<PERSON>n dokumentiert werden", "Instandsetzungskonzept"], "connections": {}, "paths": []}, "retrieval": {"original_claim": ["Das Instandsetzungskonzept schreibt vor, dass sämtliche Massnahmen mithil<PERSON> von Augmented-Reality-Brillen dokumentiert werden."], "retrievals": [{"fact": {"subject": "Instandsetzungskonzept", "predicate": "schre<PERSON>t vor", "object": "dass sämtliche Massnahmen mithilfe von Augmented-Reality-B<PERSON>n dokumentiert werden"}, "knowledge": {"subject": null, "predicate": null, "object": null}}]}, "verification": {"original_claim": ["Das Instandsetzungskonzept schreibt vor, dass sämtliche Massnahmen mithil<PERSON> von Augmented-Reality-Brillen dokumentiert werden."], "status": "INSUFFICIENT", "explanation": "No knowledge available. We can't verify something without knowledge. Verification prompt is skipped!"}}