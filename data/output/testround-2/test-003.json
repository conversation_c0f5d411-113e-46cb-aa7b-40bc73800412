{"original_claim": ["Abgereichertes Uran besitzt einen höheren Uran-235-<PERSON><PERSON>il als Natururan und wird direkt als Brennstoff verwendet."], "extraction": {"original_claim": ["Abgereichertes Uran besitzt einen höheren Uran-235-<PERSON><PERSON>il als Natururan und wird direkt als Brennstoff verwendet."], "extraction_prompt_request": "You are a precise fact extraction system. Your task is to extract atomic facts from a given text.\n\nINSTRUCTIONS:\n- Break down the input text into individual, atomic facts\n- Each fact should be a simple, standalone statement\n- Preserve the original meaning and context\n- Do not add any information not present in the original text\n- Do not make assumptions or inferences beyond what is explicitly stated\n- Each fact should be a complete sentence\n- Avoid redundancy and repetition\n- Extract only factual statements, not opinions or subjective claims\n- Return each fact as a triple in the format (subject, predicate, object)\n- Remove articles (\"the\", \"a\", \"an\", \"das\", \"die\", \"der\", etc.) from the beginning of subjects and objects\n- Keep the core entity names without articles to facilitate entity matching\n- Ensure entity names are clean and directly matchable with knowledge graph entries\n- Always keep the language of the triples in German. Do not translate or change the language of the input or output; the triples must remain in German.\n\nINPUT:\n['Abgereichertes Uran besitzt einen höheren Uran-235-Anteil als Natururan und wird direkt als Brennstoff verwendet.']\n\nOUTPUT:\nList each atomic fact as a triple (subject, predicate, object) on a new line. Remove articles from the beginning of subjects and objects.", "extraction_prompt_result": "(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Uran, <PERSON><PERSON><PERSON><PERSON>, <PERSON>öheren Uran-235-<PERSON><PERSON><PERSON> Nat<PERSON>)  \n(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Uran, wird ver<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>)", "filtering_prompt_request": "You are a precise entity and relation filtering system. Your task is to identify which entities and relations from a knowledge graph are most relevant for fact-checking a given claim.\n\nINSTRUCTIONS:\n- Analyze the claim carefully\n- Identify the key entities mentioned in the claim\n- For each entity, select the most relevant connections from the provided options\n- Focus on connections that would help verify the factual accuracy of the claim\n- Consider both direct and indirect connections that might be relevant\n- Do not add any connections that are not in the provided options\n- Return your selections in the specified format\n\nCLAIM:\n['Abgereichertes Uran besitzt einen höheren Uran-235-Anteil als Natururan und wird direkt als Brennstoff verwendet.']\n\nENTITY CONNECTIONS:\nBrennstoff: ['hat']\n\n\nOUTPUT FORMAT:\nReturn a JSON object where:\n- Keys are entity names\n- Values are arrays of connection types that are relevant for fact-checking\n- Only include connections that are in the provided options\n\nExample:\n{\n  \"Entity1\": [\"connection1\", \"connection2\"],\n  \"Entity2\": [\"connection3\"]\n}", "filtering_prompt_result": "{\n  \"<PERSON><PERSON>nstoff\": [\"hat\"]\n}", "facts": [{"subject": "Abgereichertes Uran", "predicate": "besitzt", "object": "höheren Uran-235-<PERSON><PERSON><PERSON>"}, {"subject": "Abgereichertes Uran", "predicate": "wird verwendet als", "object": "<PERSON><PERSON><PERSON><PERSON>"}], "entities": ["höheren Uran-235-<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Abgereichertes Uran"], "connections": {"Brennstoff": ["hat"]}, "paths": []}, "retrieval": {"original_claim": ["Abgereichertes Uran besitzt einen höheren Uran-235-<PERSON><PERSON>il als Natururan und wird direkt als Brennstoff verwendet."], "retrievals": [{"fact": {"subject": "Abgereichertes Uran", "predicate": "besitzt", "object": "höheren Uran-235-<PERSON><PERSON><PERSON>"}, "knowledge": {"subject": {"id": "4:73bbca3f-c54b-419b-8e4d-f90f3da3dc27:40", "name": "abgereichertes Uran", "similarity": 1.0}, "predicate": null, "object": null}}, {"fact": {"subject": "Abgereichertes Uran", "predicate": "wird verwendet als", "object": "<PERSON><PERSON><PERSON><PERSON>"}, "knowledge": {"subject": {"id": "4:73bbca3f-c54b-419b-8e4d-f90f3da3dc27:40", "name": "abgereichertes Uran", "similarity": 1.0}, "predicate": null, "object": {"id": "4:73bbca3f-c54b-419b-8e4d-f90f3da3dc27:663", "name": "<PERSON><PERSON><PERSON><PERSON>", "similarity": 1.0}}}]}, "verification": {"original_claim": ["Abgereichertes Uran besitzt einen höheren Uran-235-<PERSON><PERSON>il als Natururan und wird direkt als Brennstoff verwendet."], "status": "INSUFFICIENT", "explanation": "The provided knowledge only establishes that \"abgereichertes Uran\" (depleted uranium) is related to \"Brennstoff\" (fuel), but does not specify whether depleted uranium has a higher Uran-235 content than natural uranium, nor does it clarify if it is directly used as fuel. There is no information about the Uran-235 content in either depleted or natural uranium, nor about the direct use of depleted uranium as fuel. Additional information about the composition and usage of depleted uranium compared to natural uranium is needed to verify the claim."}}