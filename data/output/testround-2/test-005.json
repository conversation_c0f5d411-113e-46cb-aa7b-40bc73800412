{"original_claim": ["Die Abfallmatrix wird regelmäßig von der Europäischen Nuklearsicherheitsagentur evaluiert und nutzt Graphen-basiert<PERSON> Analysen."], "extraction": {"original_claim": ["Die Abfallmatrix wird regelmäßig von der Europäischen Nuklearsicherheitsagentur evaluiert und nutzt Graphen-basiert<PERSON> Analysen."], "extraction_prompt_request": "You are a precise fact extraction system. Your task is to extract atomic facts from a given text.\n\nINSTRUCTIONS:\n- Break down the input text into individual, atomic facts\n- Each fact should be a simple, standalone statement\n- Preserve the original meaning and context\n- Do not add any information not present in the original text\n- Do not make assumptions or inferences beyond what is explicitly stated\n- Each fact should be a complete sentence\n- Avoid redundancy and repetition\n- Extract only factual statements, not opinions or subjective claims\n- Return each fact as a triple in the format (subject, predicate, object)\n- Remove articles (\"the\", \"a\", \"an\", \"das\", \"die\", \"der\", etc.) from the beginning of subjects and objects\n- Keep the core entity names without articles to facilitate entity matching\n- Ensure entity names are clean and directly matchable with knowledge graph entries\n- Always keep the language of the triples in German. Do not translate or change the language of the input or output; the triples must remain in German.\n\nINPUT:\n['Die Abfallmatrix wird regelmäßig von der Europäischen Nuklearsicherheitsagentur evaluiert und nutzt Graphen-basierte Analysen.']\n\nOUTPUT:\nList each atomic fact as a triple (subject, predicate, object) on a new line. Remove articles from the beginning of subjects and objects.", "extraction_prompt_result": "(<PERSON><PERSON><PERSON><PERSON><PERSON>, wird evalui<PERSON>, Europäische Nuklearsicherheitsagentur)  \n(Abfallmatrix, nutzt, Graphen-basi<PERSON><PERSON>)", "filtering_prompt_request": "You are a precise entity and relation filtering system. Your task is to identify which entities and relations from a knowledge graph are most relevant for fact-checking a given claim.\n\nINSTRUCTIONS:\n- Analyze the claim carefully\n- Identify the key entities mentioned in the claim\n- For each entity, select the most relevant connections from the provided options\n- Focus on connections that would help verify the factual accuracy of the claim\n- Consider both direct and indirect connections that might be relevant\n- Do not add any connections that are not in the provided options\n- Return your selections in the specified format\n\nCLAIM:\n['Die Abfallmatrix wird regelmäßig von der Europäischen Nuklearsicherheitsagentur evaluiert und nutzt Graphen-basierte Analysen.']\n\nENTITY CONNECTIONS:\nAbfallmatrix: ['ist', 'hat', 'nutzt', 'nutzt', 'erzeugt']\n\n\nOUTPUT FORMAT:\nReturn a JSON object where:\n- Keys are entity names\n- Values are arrays of connection types that are relevant for fact-checking\n- Only include connections that are in the provided options\n\nExample:\n{\n  \"Entity1\": [\"connection1\", \"connection2\"],\n  \"Entity2\": [\"connection3\"]\n}", "filtering_prompt_result": "{\n  \"Abfallmatrix\": [\"nutzt\", \"nutzt\"]\n}", "facts": [{"subject": "Abfallmatrix", "predicate": "wird evaluiert von", "object": "Europäische Nuklearsicherheitsagentur"}, {"subject": "Abfallmatrix", "predicate": "nutzt", "object": "<PERSON><PERSON><PERSON><PERSON>b<PERSON><PERSON><PERSON>"}], "entities": ["<PERSON><PERSON><PERSON><PERSON>b<PERSON><PERSON><PERSON>", "Abfallmatrix", "Europäische Nuklearsicherheitsagentur"], "connections": {"Abfallmatrix": ["nutzt", "nutzt"]}, "paths": []}, "retrieval": {"original_claim": ["Die Abfallmatrix wird regelmäßig von der Europäischen Nuklearsicherheitsagentur evaluiert und nutzt Graphen-basiert<PERSON> Analysen."], "retrievals": [{"fact": {"subject": "Abfallmatrix", "predicate": "wird evaluiert von", "object": "Europäische Nuklearsicherheitsagentur"}, "knowledge": {"subject": {"id": "4:73bbca3f-c54b-419b-8e4d-f90f3da3dc27:22", "name": "Abfallmatrix", "similarity": 1.0}, "predicate": null, "object": null}}, {"fact": {"subject": "Abfallmatrix", "predicate": "nutzt", "object": "<PERSON><PERSON><PERSON><PERSON>b<PERSON><PERSON><PERSON>"}, "knowledge": {"subject": {"id": "4:73bbca3f-c54b-419b-8e4d-f90f3da3dc27:22", "name": "Abfallmatrix", "similarity": 1.0}, "predicate": null, "object": null}}]}, "verification": {"original_claim": ["Die Abfallmatrix wird regelmäßig von der Europäischen Nuklearsicherheitsagentur evaluiert und nutzt Graphen-basiert<PERSON> Analysen."], "status": "INSUFFICIENT", "explanation": "The provided knowledge only mentions the entity \"Abfallmatrix\" without any information about its evaluation by the European Nuclear Safety Agency or the use of graph-based analyses. There are no triples or statements connecting \"Abfallmatrix\" to the European Nuclear Safety Agency or to graph-based analyses. Additional information is needed to verify the claim."}}