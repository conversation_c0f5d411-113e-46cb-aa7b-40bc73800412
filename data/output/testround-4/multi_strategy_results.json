{"strategy_results": [{"strategy_name": "zero_shot", "test_results": [{"test_case_id": "test_001", "claim": "Das Standarddokument \"0E-Alterungsdossier\" enthält sowohl Daten als auch Angaben.", "expected_status": "SUPPORTS", "actual_status": "SUPPORTS", "explanation": "Die bereitgestellten Wissensgraph-Tripel zeigen, dass das \"0E-Alterungsdossier\" sowohl \"wichtige Daten\" enthält ((0E-Alterungsdossier, enthält, wichtige Daten)) als auch \"Angaben zu Wartung\" ((0E-Alterungsdossier, enth<PERSON><PERSON>, <PERSON><PERSON><PERSON> zu Wartung)). Da \"wichtige Daten\" eine Untermenge von \"Daten\" sind und \"Angaben zu Wartung\" als \"Angaben\" betrachtet werden können, wird die Behauptung, dass das Standarddokument \"0E-Alterungsdossier\" sowohl Daten als auch Angaben enthält, durch das Wissen unterstützt.", "correct": true, "extraction": {"original_claim": "Das Standarddokument \"0E-Alterungsdossier\" enthält sowohl Daten als auch Angaben.", "extraction_prompt_request": "You are a precise fact extraction system. Your task is to extract atomic facts from a given text.\n\nINSTRUCTIONS:\n- Break down the input text into individual, atomic facts\n- Each fact should be a simple, standalone statement\n- Preserve the original meaning and context\n- Do not add any information not present in the original text\n- Do not make assumptions or inferences beyond what is explicitly stated\n- Each fact should be a complete sentence\n- Avoid redundancy and repetition\n- Extract only factual statements, not opinions or subjective claims\n- Return each fact as a triple in the format (subject, predicate, object)\n- Remove articles (\"the\", \"a\", \"an\", \"das\", \"die\", \"der\", etc.) from the beginning of subjects and objects\n- Keep the core entity names without articles to facilitate entity matching\n- When extracting subjects and objects, prefer the core name or identifier of an entity, and avoid including descriptive prefixes or qualifiers\n- If context is important or clarity may be lost, you may optionally add an additional alias triple in the form: (entity, hat Bezeichnung, full_phrase), where full_phrase is the original compound form from the text.\n- Ensure entity names are clean and directly matchable with knowledge graph entries\n- Always keep the language of the triples in German. Do not translate or change the language of the input or output; the triples must remain in German.\n\nINPUT:\nDas Standarddokument \"0E-Alterungsdossier\" enthält sowohl Daten als auch Angaben.\n\nOUTPUT:\nList each atomic fact as a triple (subject, predicate, object) on a new line. Remove articles from the beginning of subjects and objects.", "extraction_prompt_result": "(Standarddokument \"0E-Alterungsdossier\", enthält, Daten)\n(Standarddokument \"0E-Alterungsdossier\", enthält, Angaben)\n(Standarddokument \"0E-Alterungsdossier\", hat Bezeichnung, Standarddokument \"0E-Alterungsdossier\")", "facts": [{"subject": "Standarddokument 0E-Alterungsdossier", "predicate": "<PERSON>th<PERSON><PERSON>", "object": "Daten"}, {"subject": "Standarddokument 0E-Alterungsdossier", "predicate": "<PERSON>th<PERSON><PERSON>", "object": "Angaben"}, {"subject": "Standarddokument 0E-Alterungsdossier", "predicate": "hat Bezeichnung", "object": "Standarddokument 0E-Alterungsdossier"}]}, "retrieval": {"original_claim": "Das Standarddokument \"0E-Alterungsdossier\" enthält sowohl Daten als auch Angaben.", "retrievals": [{"fact": {"subject": "Standarddokument 0E-Alterungsdossier", "predicate": "<PERSON>th<PERSON><PERSON>", "object": "Daten"}, "knowledge": [{"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1695", "name": "Daten", "similarity": 1.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152922604118476447", "type": "werden benutzt für"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1696", "name": "Beurteilung der Alterung", "similarity": 0.0}, "metadata": ["outgoing_relation_found_for_subject_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:366", "name": "Dokument", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152940196304519534", "type": "hat"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1695", "name": "Daten", "similarity": 1.0}, "metadata": ["incoming_relation_found_for_object_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:191", "name": "<PERSON><PERSON><PERSON><PERSON>", "similarity": 0.77}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152987475304513727", "type": "werden extrahiert aus"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:192", "name": "anlagenspezifische Betriebserfahrung", "similarity": 0.0}, "metadata": ["outgoing_relation_found_for_subject_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:211", "name": "<PERSON>", "similarity": 0.71}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152989674327769299", "type": "werden gesendet an"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:212", "name": "die Aufsichtsbehörde", "similarity": 0.0}, "metadata": ["outgoing_relation_found_for_subject_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1", "name": "wich<PERSON><PERSON> Daten", "similarity": 0.53}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152922604118474753", "type": "werden benutzt für"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:2", "name": "Beurteilung der Alterungsüberwachung", "similarity": 0.0}, "metadata": ["outgoing_relation_found_for_subject_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:0", "name": "0E-Alterungsdossier", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152921504606846976", "type": "<PERSON>th<PERSON><PERSON>"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1", "name": "wich<PERSON><PERSON> Daten", "similarity": 0.53}, "metadata": ["incoming_relation_found_for_object_entity"]}]}, {"fact": {"subject": "Standarddokument 0E-Alterungsdossier", "predicate": "<PERSON>th<PERSON><PERSON>", "object": "Angaben"}, "knowledge": [{"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:509", "name": "Angaben", "similarity": 1.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1153066640141713917", "type": "sind enthalten"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:510", "name": "im Anhang A.3 der IEC 61513", "similarity": 0.0}, "metadata": ["outgoing_relation_found_for_subject_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:577", "name": "Dosismeldung", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1153032555281252929", "type": "bein<PERSON><PERSON>"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:578", "name": "Do<PERSON><PERSON><PERSON>", "similarity": 0.74}, "metadata": ["incoming_relation_found_for_object_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:0", "name": "0E-Alterungsdossier", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1155173304420532224", "type": "<PERSON>th<PERSON><PERSON>"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:3", "name": "<PERSON><PERSON><PERSON>", "similarity": 0.56}, "metadata": ["incoming_relation_found_for_object_entity"]}]}, {"fact": {"subject": "Standarddokument 0E-Alterungsdossier", "predicate": "hat Bezeichnung", "object": "Standarddokument 0E-Alterungsdossier"}, "knowledge": []}]}}, {"test_case_id": "test_002", "claim": "Die Behörden führen Stichprobenerhebungen und Vergleichsmessungen durch.", "expected_status": "SUPPORTS", "actual_status": "SUPPORTS", "explanation": "The knowledge contains multiple triples stating that \"Behörden BAG und ENSI\" (authorities BAG and ENSI) \"führen aus\" (carry out) \"Stichprobenerhebung und Vergleichsmessungen\" (sample surveys and comparative measurements). This directly supports the claim that authorities conduct sample surveys and comparative measurements.", "correct": true, "extraction": {"original_claim": "Die Behörden führen Stichprobenerhebungen und Vergleichsmessungen durch.", "extraction_prompt_request": "You are a precise fact extraction system. Your task is to extract atomic facts from a given text.\n\nINSTRUCTIONS:\n- Break down the input text into individual, atomic facts\n- Each fact should be a simple, standalone statement\n- Preserve the original meaning and context\n- Do not add any information not present in the original text\n- Do not make assumptions or inferences beyond what is explicitly stated\n- Each fact should be a complete sentence\n- Avoid redundancy and repetition\n- Extract only factual statements, not opinions or subjective claims\n- Return each fact as a triple in the format (subject, predicate, object)\n- Remove articles (\"the\", \"a\", \"an\", \"das\", \"die\", \"der\", etc.) from the beginning of subjects and objects\n- Keep the core entity names without articles to facilitate entity matching\n- When extracting subjects and objects, prefer the core name or identifier of an entity, and avoid including descriptive prefixes or qualifiers\n- If context is important or clarity may be lost, you may optionally add an additional alias triple in the form: (entity, hat Bezeichnung, full_phrase), where full_phrase is the original compound form from the text.\n- Ensure entity names are clean and directly matchable with knowledge graph entries\n- Always keep the language of the triples in German. Do not translate or change the language of the input or output; the triples must remain in German.\n\nINPUT:\nDie Behörden führen Stichprobenerhebungen und Vergleichsmessungen durch.\n\nOUTPUT:\nList each atomic fact as a triple (subject, predicate, object) on a new line. Remove articles from the beginning of subjects and objects.", "extraction_prompt_result": "(<PERSON><PERSON><PERSON><PERSON>, führen durch, Stichprobenerhebungen)\n(<PERSON><PERSON><PERSON>rden, führen durch, Vergleichsmessungen)", "facts": [{"subject": "Behörden", "predicate": "führen durch", "object": "Stichprobenerhebungen"}, {"subject": "Behörden", "predicate": "führen durch", "object": "Vergleichsmessungen"}]}, "retrieval": {"original_claim": "Die Behörden führen Stichprobenerhebungen und Vergleichsmessungen durch.", "retrievals": [{"fact": {"subject": "Behörden", "predicate": "führen durch", "object": "Stichprobenerhebungen"}, "knowledge": [{"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1682", "name": "Sonderprüfung", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1153315129769592466", "type": "kann verlangt werden von"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1683", "name": "Aufsichtsbehörden", "similarity": 0.64}, "metadata": ["incoming_relation_found_for_object_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:47", "name": "Behörden BAG und ENSI", "similarity": 0.55}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152945693862658095", "type": "führen aus"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:48", "name": "Stichprobenerhebung und Vergleichsmessungen", "similarity": 0.0}, "metadata": ["outgoing_relation_found_for_subject_entity"]}]}, {"fact": {"subject": "Behörden", "predicate": "führen durch", "object": "Vergleichsmessungen"}, "knowledge": [{"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:47", "name": "Behörden BAG und ENSI", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152945693862658095", "type": "führen aus"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:48", "name": "Stichprobenerhebung und Vergleichsmessungen", "similarity": 0.61}, "metadata": ["incoming_relation_found_for_object_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1682", "name": "Sonderprüfung", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1153315129769592466", "type": "kann verlangt werden von"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1683", "name": "Aufsichtsbehörden", "similarity": 0.64}, "metadata": ["incoming_relation_found_for_object_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:47", "name": "Behörden BAG und ENSI", "similarity": 0.55}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152945693862658095", "type": "führen aus"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:48", "name": "Stichprobenerhebung und Vergleichsmessungen", "similarity": 0.61}, "metadata": ["direct_relation_found_between_subject_and_object"]}]}]}}, {"test_case_id": "test_003", "claim": "Abgereichertes Uran besitzt einen höheren Uran-235-<PERSON><PERSON>il als Natururan und wird direkt als Brennstoff verwendet.", "expected_status": "REFUTES", "actual_status": "REFUTES", "explanation": "Die bereitgestellten Informationen besagen, dass abgereichertes Uran einen \"geringeren Anteil\" hat. <PERSON> sich dies auf den Anteil von Uran-235 bezieht, widerspricht dies der Behauptung, abgereichertes Uran habe einen höheren Uran-235-<PERSON><PERSON><PERSON> als Natururan. Es gibt zudem keine Information, dass abgereichertes Uran direkt als Brennstoff verwendet wird. Die Aussage wird daher durch das Wissen widerlegt.", "correct": true, "extraction": {"original_claim": "Abgereichertes Uran besitzt einen höheren Uran-235-<PERSON><PERSON>il als Natururan und wird direkt als Brennstoff verwendet.", "extraction_prompt_request": "You are a precise fact extraction system. Your task is to extract atomic facts from a given text.\n\nINSTRUCTIONS:\n- Break down the input text into individual, atomic facts\n- Each fact should be a simple, standalone statement\n- Preserve the original meaning and context\n- Do not add any information not present in the original text\n- Do not make assumptions or inferences beyond what is explicitly stated\n- Each fact should be a complete sentence\n- Avoid redundancy and repetition\n- Extract only factual statements, not opinions or subjective claims\n- Return each fact as a triple in the format (subject, predicate, object)\n- Remove articles (\"the\", \"a\", \"an\", \"das\", \"die\", \"der\", etc.) from the beginning of subjects and objects\n- Keep the core entity names without articles to facilitate entity matching\n- When extracting subjects and objects, prefer the core name or identifier of an entity, and avoid including descriptive prefixes or qualifiers\n- If context is important or clarity may be lost, you may optionally add an additional alias triple in the form: (entity, hat Bezeichnung, full_phrase), where full_phrase is the original compound form from the text.\n- Ensure entity names are clean and directly matchable with knowledge graph entries\n- Always keep the language of the triples in German. Do not translate or change the language of the input or output; the triples must remain in German.\n\nINPUT:\nAbgereichertes Uran besitzt einen höheren Uran-235-Anteil als Natururan und wird direkt als Brennstoff verwendet.\n\nOUTPUT:\nList each atomic fact as a triple (subject, predicate, object) on a new line. Remove articles from the beginning of subjects and objects.", "extraction_prompt_result": "(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Uran, <PERSON><PERSON><PERSON><PERSON>, <PERSON>öheren Uran-235-<PERSON><PERSON><PERSON> Nat<PERSON>)\n(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Uran, wird ver<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>)", "facts": [{"subject": "Abgereichertes Uran", "predicate": "besitzt", "object": "höheren Uran-235-<PERSON><PERSON><PERSON>"}, {"subject": "Abgereichertes Uran", "predicate": "wird verwendet als", "object": "<PERSON><PERSON><PERSON><PERSON>"}]}, "retrieval": {"original_claim": "Abgereichertes Uran besitzt einen höheren Uran-235-<PERSON><PERSON>il als Natururan und wird direkt als Brennstoff verwendet.", "retrievals": [{"fact": {"subject": "Abgereichertes Uran", "predicate": "besitzt", "object": "höheren Uran-235-<PERSON><PERSON><PERSON>"}, "knowledge": [{"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:52", "name": "abgereichertes Uran", "similarity": 1.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152923703630102580", "type": "ist"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:54", "name": "Uran", "similarity": 0.0}, "metadata": ["outgoing_relation_found_for_subject_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:52", "name": "abgereichertes Uran", "similarity": 1.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152940196304519220", "type": "hat"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:53", "name": "<PERSON><PERSON><PERSON><PERSON>", "similarity": 0.0}, "metadata": ["outgoing_relation_found_for_subject_entity"]}]}, {"fact": {"subject": "Abgereichertes Uran", "predicate": "wird verwendet als", "object": "<PERSON><PERSON><PERSON><PERSON>"}, "knowledge": [{"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:451", "name": "Brennstab", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1153057844048691651", "type": "ist gefüllt mit"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:453", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "similarity": 0.83}, "metadata": ["incoming_relation_found_for_object_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:2066", "name": "Oxid-<PERSON><PERSON><PERSON><PERSON>", "similarity": 0.8}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152995171885910034", "type": "wird"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:2067", "name": "chemisch aufgelöst", "similarity": 0.0}, "metadata": ["outgoing_relation_found_for_subject_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1554", "name": "<PERSON>sen Folge", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1153060043071948306", "type": "tritt auf"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1555", "name": "ein Brennst<PERSON>", "similarity": 0.61}, "metadata": ["incoming_relation_found_for_object_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:52", "name": "abgereichertes Uran", "similarity": 1.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152923703630102580", "type": "ist"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:54", "name": "Uran", "similarity": 0.0}, "metadata": ["outgoing_relation_found_for_subject_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:52", "name": "abgereichertes Uran", "similarity": 1.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152940196304519220", "type": "hat"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:53", "name": "<PERSON><PERSON><PERSON><PERSON>", "similarity": 0.0}, "metadata": ["outgoing_relation_found_for_subject_entity"]}]}]}}, {"test_case_id": "test_004", "claim": "Radioaktive Abfälle werden nicht im Hauptlager eingelagert, sondern bleiben grundsätzlich unverpackt.", "expected_status": "REFUTES", "actual_status": "INSUFFICIENT", "explanation": "No knowledge available. We can't verify something without knowledge. Verification prompt is skipped!", "correct": false, "extraction": {"original_claim": "Radioaktive Abfälle werden nicht im Hauptlager eingelagert, sondern bleiben grundsätzlich unverpackt.", "extraction_prompt_request": "You are a precise fact extraction system. Your task is to extract atomic facts from a given text.\n\nINSTRUCTIONS:\n- Break down the input text into individual, atomic facts\n- Each fact should be a simple, standalone statement\n- Preserve the original meaning and context\n- Do not add any information not present in the original text\n- Do not make assumptions or inferences beyond what is explicitly stated\n- Each fact should be a complete sentence\n- Avoid redundancy and repetition\n- Extract only factual statements, not opinions or subjective claims\n- Return each fact as a triple in the format (subject, predicate, object)\n- Remove articles (\"the\", \"a\", \"an\", \"das\", \"die\", \"der\", etc.) from the beginning of subjects and objects\n- Keep the core entity names without articles to facilitate entity matching\n- When extracting subjects and objects, prefer the core name or identifier of an entity, and avoid including descriptive prefixes or qualifiers\n- If context is important or clarity may be lost, you may optionally add an additional alias triple in the form: (entity, hat Bezeichnung, full_phrase), where full_phrase is the original compound form from the text.\n- Ensure entity names are clean and directly matchable with knowledge graph entries\n- Always keep the language of the triples in German. Do not translate or change the language of the input or output; the triples must remain in German.\n\nINPUT:\nRadioaktive Abfälle werden nicht im Hauptlager eingelagert, sondern bleiben grundsätzlich unverpackt.\n\nOUTPUT:\nList each atomic fact as a triple (subject, predicate, object) on a new line. Remove articles from the beginning of subjects and objects.", "extraction_prompt_result": "Radioaktive Abfälle, werden nicht eingelagert, Hauptlager  \nRadioaktive Abfälle, b<PERSON>iben, grundsätzlich unverpackt", "facts": []}, "retrieval": {"original_claim": "Radioaktive Abfälle werden nicht im Hauptlager eingelagert, sondern bleiben grundsätzlich unverpackt.", "retrievals": []}}, {"test_case_id": "test_005", "claim": "Die Abfallmatrix wird regelmäßig von der Europäischen Nuklearsicherheitsagentur evaluiert und nutzt Graphen-basiert<PERSON> Analysen.", "expected_status": "INSUFFICIENT", "actual_status": "INSUFFICIENT", "explanation": "Die bereitgestellten Tripel enthalten Informationen darüber, was eine Abfallmatrix ist, wie sie entsteht und wofür sie verwendet wird (z.B. Einbettung radioaktiver Abfälle, Erzeugung durch Bindemittel). <PERSON>s gibt jedoch keine <PERSON> darüber, ob die Abfallmatrix regelmäßig von der Europäischen Nuklearsicherheitsagentur evaluiert wird oder ob Graphen-basierte Ana<PERSON>sen dabei genutzt werden. Um den Anspruch zu überprüfen, wären explizite Angaben zu Evaluationsprozessen durch die genannte Agentur und zur Verwendung von Graphen-basierten Ana<PERSON>sen erforderlich.", "correct": true, "extraction": {"original_claim": "Die Abfallmatrix wird regelmäßig von der Europäischen Nuklearsicherheitsagentur evaluiert und nutzt Graphen-basiert<PERSON> Analysen.", "extraction_prompt_request": "You are a precise fact extraction system. Your task is to extract atomic facts from a given text.\n\nINSTRUCTIONS:\n- Break down the input text into individual, atomic facts\n- Each fact should be a simple, standalone statement\n- Preserve the original meaning and context\n- Do not add any information not present in the original text\n- Do not make assumptions or inferences beyond what is explicitly stated\n- Each fact should be a complete sentence\n- Avoid redundancy and repetition\n- Extract only factual statements, not opinions or subjective claims\n- Return each fact as a triple in the format (subject, predicate, object)\n- Remove articles (\"the\", \"a\", \"an\", \"das\", \"die\", \"der\", etc.) from the beginning of subjects and objects\n- Keep the core entity names without articles to facilitate entity matching\n- When extracting subjects and objects, prefer the core name or identifier of an entity, and avoid including descriptive prefixes or qualifiers\n- If context is important or clarity may be lost, you may optionally add an additional alias triple in the form: (entity, hat Bezeichnung, full_phrase), where full_phrase is the original compound form from the text.\n- Ensure entity names are clean and directly matchable with knowledge graph entries\n- Always keep the language of the triples in German. Do not translate or change the language of the input or output; the triples must remain in German.\n\nINPUT:\nDie Abfallmatrix wird regelm<PERSON><PERSON><PERSON> von der Europäischen Nuklearsicherheitsagentur evaluiert und nutzt Graphen-basierte Analysen.\n\nOUTPUT:\nList each atomic fact as a triple (subject, predicate, object) on a new line. Remove articles from the beginning of subjects and objects.", "extraction_prompt_result": "(<PERSON><PERSON><PERSON><PERSON><PERSON>, wird evalui<PERSON>, Europäische Nuklearsicherheitsagentur)\n(Abfallmatrix, nutzt, Graphen-basierte Analysen)\n(Europäische Nuklearsicherheitsagentur, hat Bezeichnung, der Europäischen Nuklearsicherheitsagentur)\n(Graphen-basiert<PERSON>, hat <PERSON>zeichnung, Graphen-basiert<PERSON>)", "facts": [{"subject": "Abfallmatrix", "predicate": "wird evaluiert von", "object": "Europäische Nuklearsicherheitsagentur"}, {"subject": "Abfallmatrix", "predicate": "nutzt", "object": "<PERSON><PERSON><PERSON><PERSON>b<PERSON><PERSON><PERSON>"}, {"subject": "Europäische Nuklearsicherheitsagentur", "predicate": "hat Bezeichnung", "object": "der Europäischen Nuklearsicherheitsagentur"}, {"subject": "<PERSON><PERSON><PERSON><PERSON>b<PERSON><PERSON><PERSON>", "predicate": "hat Bezeichnung", "object": "<PERSON><PERSON><PERSON><PERSON>b<PERSON><PERSON><PERSON>"}]}, "retrieval": {"original_claim": "Die Abfallmatrix wird regelmäßig von der Europäischen Nuklearsicherheitsagentur evaluiert und nutzt Graphen-basiert<PERSON> Analysen.", "retrievals": [{"fact": {"subject": "Abfallmatrix", "predicate": "wird evaluiert von", "object": "Europäische Nuklearsicherheitsagentur"}, "knowledge": [{"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:37", "name": "radioaktive Abfälle", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1153179889839374373", "type": "werden eingebettet in"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1054", "name": "Abfallmatrix", "similarity": 1.0}, "metadata": ["incoming_relation_found_for_object_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:28", "name": "Eine Abfallmatrix", "similarity": 0.83}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152923703630102556", "type": "ist"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:29", "name": "ein spezielles Abfallprodukt", "similarity": 0.0}, "metadata": ["outgoing_relation_found_for_subject_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:40", "name": "Bindemittel", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1153049047955669032", "type": "<PERSON><PERSON><PERSON>"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:417", "name": "eine Abfallmatrix", "similarity": 0.83}, "metadata": ["incoming_relation_found_for_object_entity"]}]}, {"fact": {"subject": "Abfallmatrix", "predicate": "nutzt", "object": "<PERSON><PERSON><PERSON><PERSON>b<PERSON><PERSON><PERSON>"}, "knowledge": [{"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:37", "name": "radioaktive Abfälle", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1153179889839374373", "type": "werden eingebettet in"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1054", "name": "Abfallmatrix", "similarity": 1.0}, "metadata": ["incoming_relation_found_for_object_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:28", "name": "Eine Abfallmatrix", "similarity": 0.83}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152923703630102556", "type": "ist"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:29", "name": "ein spezielles Abfallprodukt", "similarity": 0.0}, "metadata": ["outgoing_relation_found_for_subject_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:40", "name": "Bindemittel", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1153049047955669032", "type": "<PERSON><PERSON><PERSON>"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:417", "name": "eine Abfallmatrix", "similarity": 0.83}, "metadata": ["incoming_relation_found_for_object_entity"]}]}, {"fact": {"subject": "Europäische Nuklearsicherheitsagentur", "predicate": "hat Bezeichnung", "object": "der Europäischen Nuklearsicherheitsagentur"}, "knowledge": []}, {"fact": {"subject": "<PERSON><PERSON><PERSON><PERSON>b<PERSON><PERSON><PERSON>", "predicate": "hat Bezeichnung", "object": "<PERSON><PERSON><PERSON><PERSON>b<PERSON><PERSON><PERSON>"}, "knowledge": []}]}}, {"test_case_id": "test_006", "claim": "Das Instandsetzungskonzept schreibt vor, dass sämtliche Massnahmen mithil<PERSON> von Augmented-Reality-Brillen dokumentiert werden.", "expected_status": "INSUFFICIENT", "actual_status": "INSUFFICIENT", "explanation": "No knowledge available. We can't verify something without knowledge. Verification prompt is skipped!", "correct": true, "extraction": {"original_claim": "Das Instandsetzungskonzept schreibt vor, dass sämtliche Massnahmen mithil<PERSON> von Augmented-Reality-Brillen dokumentiert werden.", "extraction_prompt_request": "You are a precise fact extraction system. Your task is to extract atomic facts from a given text.\n\nINSTRUCTIONS:\n- Break down the input text into individual, atomic facts\n- Each fact should be a simple, standalone statement\n- Preserve the original meaning and context\n- Do not add any information not present in the original text\n- Do not make assumptions or inferences beyond what is explicitly stated\n- Each fact should be a complete sentence\n- Avoid redundancy and repetition\n- Extract only factual statements, not opinions or subjective claims\n- Return each fact as a triple in the format (subject, predicate, object)\n- Remove articles (\"the\", \"a\", \"an\", \"das\", \"die\", \"der\", etc.) from the beginning of subjects and objects\n- Keep the core entity names without articles to facilitate entity matching\n- When extracting subjects and objects, prefer the core name or identifier of an entity, and avoid including descriptive prefixes or qualifiers\n- If context is important or clarity may be lost, you may optionally add an additional alias triple in the form: (entity, hat Bezeichnung, full_phrase), where full_phrase is the original compound form from the text.\n- Ensure entity names are clean and directly matchable with knowledge graph entries\n- Always keep the language of the triples in German. Do not translate or change the language of the input or output; the triples must remain in German.\n\nINPUT:\nDas Instandsetzungskonzept schre<PERSON> vor, dass sämtliche Massnahmen mithilfe von Augmented-Reality-Brillen dokumentiert werden.\n\nOUTPUT:\nList each atomic fact as a triple (subject, predicate, object) on a new line. Remove articles from the beginning of subjects and objects.", "extraction_prompt_result": "(Instandsetzungskonzept, sch<PERSON><PERSON><PERSON> vor, dass sämtliche Massnahmen mithilfe von Augmented-Reality-Brillen dokumentiert werden)\n(Instandsetzungskonzept, hat Bezeichnung, Das Instandsetzungskonzept)\n(Augmented-Reality-Brillen, werden verwendet für, Do<PERSON><PERSON> von Massnahmen)", "facts": [{"subject": "Instandsetzungskonzept", "predicate": "schre<PERSON>t vor", "object": "dass sämtliche Massnahmen mithilfe von Augmented-Reality-B<PERSON>n dokumentiert werden"}, {"subject": "Instandsetzungskonzept", "predicate": "hat Bezeichnung", "object": "Das Instandsetzungskonzept"}, {"subject": "Augmented-Reality-<PERSON><PERSON><PERSON>", "predicate": "werden verwendet für", "object": "<PERSON><PERSON><PERSON> von <PERSON>"}]}, "retrieval": {"original_claim": "Das Instandsetzungskonzept schreibt vor, dass sämtliche Massnahmen mithil<PERSON> von Augmented-Reality-Brillen dokumentiert werden.", "retrievals": [{"fact": {"subject": "Instandsetzungskonzept", "predicate": "schre<PERSON>t vor", "object": "dass sämtliche Massnahmen mithilfe von Augmented-Reality-B<PERSON>n dokumentiert werden"}, "knowledge": []}, {"fact": {"subject": "Instandsetzungskonzept", "predicate": "hat Bezeichnung", "object": "Das Instandsetzungskonzept"}, "knowledge": []}, {"fact": {"subject": "Augmented-Reality-<PERSON><PERSON><PERSON>", "predicate": "werden verwendet für", "object": "<PERSON><PERSON><PERSON> von <PERSON>"}, "knowledge": []}]}}]}, {"strategy_name": "few_shot", "test_results": [{"test_case_id": "test_001", "claim": "Das Standarddokument \"0E-Alterungsdossier\" enthält sowohl Daten als auch Angaben.", "expected_status": "SUPPORTS", "actual_status": "SUPPORTS", "explanation": "Die bereitgestellten Wissensgraph-Tripel zeigen, dass das \"0E-Alterungsdossier\" sowohl \"wichtige Daten\" enthält ((0E-Alterungsdossier, enthält, wichtige Daten)) als auch \"Angaben zu Wartung\" ((0E-Alterungsdossier, enth<PERSON><PERSON>, <PERSON><PERSON><PERSON> zu Wartung)). <PERSON> sowohl \"Daten\" als auch \"Angaben\" explizit als enthalten genannt werden, wird die Behauptung unterstützt.", "correct": true, "extraction": {"original_claim": "Das Standarddokument \"0E-Alterungsdossier\" enthält sowohl Daten als auch Angaben.", "extraction_prompt_request": "You are a precise fact extraction system. Your task is to extract atomic facts from a given text.\n\nINSTRUCTIONS:\n- Break down the input text into individual, atomic facts\n- Each fact should be a simple, standalone statement\n- Preserve the original meaning and context\n- Do not add any information not present in the original text\n- Do not make assumptions or inferences beyond what is explicitly stated\n- Each fact should be a complete sentence\n- Avoid redundancy and repetition\n- Extract only factual statements, not opinions or subjective claims\n- Return each fact as a triple in the format (subject, predicate, object)\n- Remove articles (\"the\", \"a\", \"an\", \"das\", \"die\", \"der\", etc.) from the beginning of subjects and objects\n- Keep the core entity names without articles to facilitate entity matching\n- When extracting subjects and objects, prefer the core name or identifier of an entity, and avoid including descriptive prefixes or qualifiers\n- If context is important or clarity may be lost, you may optionally add an additional alias triple in the form: (entity, hat Bezeichnung, full_phrase), where full_phrase is the original compound form from the text.\n- Ensure entity names are clean and directly matchable with knowledge graph entries\n- Always keep the language of the triples in German. Do not translate or change the language of the input or output; the triples must remain in German.\n\n### EXAMPLES ###\n\nINPUT:\n\"Reaktorsicherheitskommission erstellt regelmäßig Berichte über die Anlagensicherheit.\"\n\nOUTPUT:\n(Reaktorsicherheitskommission, erstellt, Berichte über Anlagensicherheit)\n\n---\n\nINPUT:\n\"Primärkühlmittelkreislauf transportiert Wärme von Reaktorkern zu Wärmetauscher.\"\n\nOUTPUT:\n(Primärkühlmittelkreislauf, transportiert, Wärme)\n(Wärme, wird transportiert zu, Wärmetauscher)\n(Wärme, stammt aus, Reaktorkern)\n\n---\n\nINPUT:\n\"Jod-131 hat eine Halbwertszeit von acht Tagen und wird bei Kernspaltungsprozessen freigesetzt.\"\n\nOUTPUT:\n(Jod-131, hat, Halbwertszeit von acht Tagen)\n(Jod-131, wird freigesetzt bei, Kernspaltungsprozesse)\n\n---\n\n### Now process the following input:\n\nINPUT:\nDas Standarddokument \"0E-Alterungsdossier\" enthält sowohl Daten als auch Angaben.\n\nOUTPUT:\nList each atomic fact as a triple (subject, predicate, object) on a new line.", "extraction_prompt_result": "(Standarddokument 0E-Alterungsdossier, enthält, Daten)\n(Standarddokument 0E-Alterungsdossier, enthält, Angaben)\n(Standarddokument 0E-Alterungsdossier, hat <PERSON>ng, Standarddokument \"0E-Alterungsdossier\")", "facts": [{"subject": "Standarddokument 0E-Alterungsdossier", "predicate": "<PERSON>th<PERSON><PERSON>", "object": "Daten"}, {"subject": "Standarddokument 0E-Alterungsdossier", "predicate": "<PERSON>th<PERSON><PERSON>", "object": "Angaben"}, {"subject": "Standarddokument 0E-Alterungsdossier", "predicate": "hat Bezeichnung", "object": "Standarddokument 0E-Alterungsdossier"}]}, "retrieval": {"original_claim": "Das Standarddokument \"0E-Alterungsdossier\" enthält sowohl Daten als auch Angaben.", "retrievals": [{"fact": {"subject": "Standarddokument 0E-Alterungsdossier", "predicate": "<PERSON>th<PERSON><PERSON>", "object": "Daten"}, "knowledge": [{"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1695", "name": "Daten", "similarity": 1.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152922604118476447", "type": "werden benutzt für"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1696", "name": "Beurteilung der Alterung", "similarity": 0.0}, "metadata": ["outgoing_relation_found_for_subject_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:366", "name": "Dokument", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152940196304519534", "type": "hat"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1695", "name": "Daten", "similarity": 1.0}, "metadata": ["incoming_relation_found_for_object_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:191", "name": "<PERSON><PERSON><PERSON><PERSON>", "similarity": 0.77}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152987475304513727", "type": "werden extrahiert aus"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:192", "name": "anlagenspezifische Betriebserfahrung", "similarity": 0.0}, "metadata": ["outgoing_relation_found_for_subject_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:211", "name": "<PERSON>", "similarity": 0.71}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152989674327769299", "type": "werden gesendet an"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:212", "name": "die Aufsichtsbehörde", "similarity": 0.0}, "metadata": ["outgoing_relation_found_for_subject_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1", "name": "wich<PERSON><PERSON> Daten", "similarity": 0.53}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152922604118474753", "type": "werden benutzt für"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:2", "name": "Beurteilung der Alterungsüberwachung", "similarity": 0.0}, "metadata": ["outgoing_relation_found_for_subject_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:0", "name": "0E-Alterungsdossier", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152921504606846976", "type": "<PERSON>th<PERSON><PERSON>"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1", "name": "wich<PERSON><PERSON> Daten", "similarity": 0.53}, "metadata": ["incoming_relation_found_for_object_entity"]}]}, {"fact": {"subject": "Standarddokument 0E-Alterungsdossier", "predicate": "<PERSON>th<PERSON><PERSON>", "object": "Angaben"}, "knowledge": [{"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:509", "name": "Angaben", "similarity": 1.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1153066640141713917", "type": "sind enthalten"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:510", "name": "im Anhang A.3 der IEC 61513", "similarity": 0.0}, "metadata": ["outgoing_relation_found_for_subject_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:577", "name": "Dosismeldung", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1153032555281252929", "type": "bein<PERSON><PERSON>"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:578", "name": "Do<PERSON><PERSON><PERSON>", "similarity": 0.74}, "metadata": ["incoming_relation_found_for_object_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:0", "name": "0E-Alterungsdossier", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1155173304420532224", "type": "<PERSON>th<PERSON><PERSON>"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:3", "name": "<PERSON><PERSON><PERSON>", "similarity": 0.56}, "metadata": ["incoming_relation_found_for_object_entity"]}]}, {"fact": {"subject": "Standarddokument 0E-Alterungsdossier", "predicate": "hat Bezeichnung", "object": "Standarddokument 0E-Alterungsdossier"}, "knowledge": []}]}}, {"test_case_id": "test_002", "claim": "Die Behörden führen Stichprobenerhebungen und Vergleichsmessungen durch.", "expected_status": "SUPPORTS", "actual_status": "SUPPORTS", "explanation": "The knowledge contains multiple triples stating that \"Behörden BAG und ENSI\" (authorities BAG and ENSI) \"führen aus\" (carry out) \"Stichprobenerhebung und Vergleichsmessungen\" (sample surveys and comparative measurements). This directly supports the claim that authorities conduct sample surveys and comparative measurements.", "correct": true, "extraction": {"original_claim": "Die Behörden führen Stichprobenerhebungen und Vergleichsmessungen durch.", "extraction_prompt_request": "You are a precise fact extraction system. Your task is to extract atomic facts from a given text.\n\nINSTRUCTIONS:\n- Break down the input text into individual, atomic facts\n- Each fact should be a simple, standalone statement\n- Preserve the original meaning and context\n- Do not add any information not present in the original text\n- Do not make assumptions or inferences beyond what is explicitly stated\n- Each fact should be a complete sentence\n- Avoid redundancy and repetition\n- Extract only factual statements, not opinions or subjective claims\n- Return each fact as a triple in the format (subject, predicate, object)\n- Remove articles (\"the\", \"a\", \"an\", \"das\", \"die\", \"der\", etc.) from the beginning of subjects and objects\n- Keep the core entity names without articles to facilitate entity matching\n- When extracting subjects and objects, prefer the core name or identifier of an entity, and avoid including descriptive prefixes or qualifiers\n- If context is important or clarity may be lost, you may optionally add an additional alias triple in the form: (entity, hat Bezeichnung, full_phrase), where full_phrase is the original compound form from the text.\n- Ensure entity names are clean and directly matchable with knowledge graph entries\n- Always keep the language of the triples in German. Do not translate or change the language of the input or output; the triples must remain in German.\n\n### EXAMPLES ###\n\nINPUT:\n\"Reaktorsicherheitskommission erstellt regelmäßig Berichte über die Anlagensicherheit.\"\n\nOUTPUT:\n(Reaktorsicherheitskommission, erstellt, Berichte über Anlagensicherheit)\n\n---\n\nINPUT:\n\"Primärkühlmittelkreislauf transportiert Wärme von Reaktorkern zu Wärmetauscher.\"\n\nOUTPUT:\n(Primärkühlmittelkreislauf, transportiert, Wärme)\n(Wärme, wird transportiert zu, Wärmetauscher)\n(Wärme, stammt aus, Reaktorkern)\n\n---\n\nINPUT:\n\"Jod-131 hat eine Halbwertszeit von acht Tagen und wird bei Kernspaltungsprozessen freigesetzt.\"\n\nOUTPUT:\n(Jod-131, hat, Halbwertszeit von acht Tagen)\n(Jod-131, wird freigesetzt bei, Kernspaltungsprozesse)\n\n---\n\n### Now process the following input:\n\nINPUT:\nDie Behörden führen Stichprobenerhebungen und Vergleichsmessungen durch.\n\nOUTPUT:\nList each atomic fact as a triple (subject, predicate, object) on a new line.", "extraction_prompt_result": "(<PERSON><PERSON><PERSON><PERSON>, führen durch, Stichprobenerhebungen)\n(<PERSON><PERSON><PERSON>rden, führen durch, Vergleichsmessungen)", "facts": [{"subject": "Behörden", "predicate": "führen durch", "object": "Stichprobenerhebungen"}, {"subject": "Behörden", "predicate": "führen durch", "object": "Vergleichsmessungen"}]}, "retrieval": {"original_claim": "Die Behörden führen Stichprobenerhebungen und Vergleichsmessungen durch.", "retrievals": [{"fact": {"subject": "Behörden", "predicate": "führen durch", "object": "Stichprobenerhebungen"}, "knowledge": [{"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1682", "name": "Sonderprüfung", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1153315129769592466", "type": "kann verlangt werden von"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1683", "name": "Aufsichtsbehörden", "similarity": 0.64}, "metadata": ["incoming_relation_found_for_object_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:47", "name": "Behörden BAG und ENSI", "similarity": 0.55}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152945693862658095", "type": "führen aus"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:48", "name": "Stichprobenerhebung und Vergleichsmessungen", "similarity": 0.0}, "metadata": ["outgoing_relation_found_for_subject_entity"]}]}, {"fact": {"subject": "Behörden", "predicate": "führen durch", "object": "Vergleichsmessungen"}, "knowledge": [{"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:47", "name": "Behörden BAG und ENSI", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152945693862658095", "type": "führen aus"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:48", "name": "Stichprobenerhebung und Vergleichsmessungen", "similarity": 0.61}, "metadata": ["incoming_relation_found_for_object_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1682", "name": "Sonderprüfung", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1153315129769592466", "type": "kann verlangt werden von"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1683", "name": "Aufsichtsbehörden", "similarity": 0.64}, "metadata": ["incoming_relation_found_for_object_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:47", "name": "Behörden BAG und ENSI", "similarity": 0.55}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152945693862658095", "type": "führen aus"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:48", "name": "Stichprobenerhebung und Vergleichsmessungen", "similarity": 0.61}, "metadata": ["direct_relation_found_between_subject_and_object"]}]}]}}, {"test_case_id": "test_003", "claim": "Abgereichertes Uran besitzt einen höheren Uran-235-<PERSON><PERSON>il als Natururan und wird direkt als Brennstoff verwendet.", "expected_status": "REFUTES", "actual_status": "REFUTES", "explanation": "Die bereitgestellten Informationen besagen, dass abgereichertes Uran einen \"geringeren Anteil\" hat. <PERSON> sich dies auf den Anteil von Uran-235 bezieht, widerspricht dies der Behauptung, abgereichertes Uran habe einen höheren Uran-235-Ante<PERSON> als Natururan. Außerdem gibt es keine Information, dass abgereichertes Uran direkt als Brennstoff verwendet wird. Die Aussage wird somit durch die vorhandenen Fakten widerlegt.", "correct": true, "extraction": {"original_claim": "Abgereichertes Uran besitzt einen höheren Uran-235-<PERSON><PERSON>il als Natururan und wird direkt als Brennstoff verwendet.", "extraction_prompt_request": "You are a precise fact extraction system. Your task is to extract atomic facts from a given text.\n\nINSTRUCTIONS:\n- Break down the input text into individual, atomic facts\n- Each fact should be a simple, standalone statement\n- Preserve the original meaning and context\n- Do not add any information not present in the original text\n- Do not make assumptions or inferences beyond what is explicitly stated\n- Each fact should be a complete sentence\n- Avoid redundancy and repetition\n- Extract only factual statements, not opinions or subjective claims\n- Return each fact as a triple in the format (subject, predicate, object)\n- Remove articles (\"the\", \"a\", \"an\", \"das\", \"die\", \"der\", etc.) from the beginning of subjects and objects\n- Keep the core entity names without articles to facilitate entity matching\n- When extracting subjects and objects, prefer the core name or identifier of an entity, and avoid including descriptive prefixes or qualifiers\n- If context is important or clarity may be lost, you may optionally add an additional alias triple in the form: (entity, hat Bezeichnung, full_phrase), where full_phrase is the original compound form from the text.\n- Ensure entity names are clean and directly matchable with knowledge graph entries\n- Always keep the language of the triples in German. Do not translate or change the language of the input or output; the triples must remain in German.\n\n### EXAMPLES ###\n\nINPUT:\n\"Reaktorsicherheitskommission erstellt regelmäßig Berichte über die Anlagensicherheit.\"\n\nOUTPUT:\n(Reaktorsicherheitskommission, erstellt, Berichte über Anlagensicherheit)\n\n---\n\nINPUT:\n\"Primärkühlmittelkreislauf transportiert Wärme von Reaktorkern zu Wärmetauscher.\"\n\nOUTPUT:\n(Primärkühlmittelkreislauf, transportiert, Wärme)\n(Wärme, wird transportiert zu, Wärmetauscher)\n(Wärme, stammt aus, Reaktorkern)\n\n---\n\nINPUT:\n\"Jod-131 hat eine Halbwertszeit von acht Tagen und wird bei Kernspaltungsprozessen freigesetzt.\"\n\nOUTPUT:\n(Jod-131, hat, Halbwertszeit von acht Tagen)\n(Jod-131, wird freigesetzt bei, Kernspaltungsprozesse)\n\n---\n\n### Now process the following input:\n\nINPUT:\nAbgereichertes Uran besitzt einen höheren Uran-235-Anteil als Natururan und wird direkt als Brennstoff verwendet.\n\nOUTPUT:\nList each atomic fact as a triple (subject, predicate, object) on a new line.", "extraction_prompt_result": "(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Uran, <PERSON><PERSON><PERSON><PERSON>, <PERSON>öheren Uran-235-<PERSON><PERSON><PERSON> Nat<PERSON>)\n(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Uran, wird ver<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>)", "facts": [{"subject": "Abgereichertes Uran", "predicate": "besitzt", "object": "höheren Uran-235-<PERSON><PERSON><PERSON>"}, {"subject": "Abgereichertes Uran", "predicate": "wird verwendet als", "object": "<PERSON><PERSON><PERSON><PERSON>"}]}, "retrieval": {"original_claim": "Abgereichertes Uran besitzt einen höheren Uran-235-<PERSON><PERSON>il als Natururan und wird direkt als Brennstoff verwendet.", "retrievals": [{"fact": {"subject": "Abgereichertes Uran", "predicate": "besitzt", "object": "höheren Uran-235-<PERSON><PERSON><PERSON>"}, "knowledge": [{"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:52", "name": "abgereichertes Uran", "similarity": 1.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152923703630102580", "type": "ist"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:54", "name": "Uran", "similarity": 0.0}, "metadata": ["outgoing_relation_found_for_subject_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:52", "name": "abgereichertes Uran", "similarity": 1.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152940196304519220", "type": "hat"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:53", "name": "<PERSON><PERSON><PERSON><PERSON>", "similarity": 0.0}, "metadata": ["outgoing_relation_found_for_subject_entity"]}]}, {"fact": {"subject": "Abgereichertes Uran", "predicate": "wird verwendet als", "object": "<PERSON><PERSON><PERSON><PERSON>"}, "knowledge": [{"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:451", "name": "Brennstab", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1153057844048691651", "type": "ist gefüllt mit"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:453", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "similarity": 0.83}, "metadata": ["incoming_relation_found_for_object_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:2066", "name": "Oxid-<PERSON><PERSON><PERSON><PERSON>", "similarity": 0.8}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152995171885910034", "type": "wird"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:2067", "name": "chemisch aufgelöst", "similarity": 0.0}, "metadata": ["outgoing_relation_found_for_subject_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1554", "name": "<PERSON>sen Folge", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1153060043071948306", "type": "tritt auf"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1555", "name": "ein Brennst<PERSON>", "similarity": 0.61}, "metadata": ["incoming_relation_found_for_object_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:52", "name": "abgereichertes Uran", "similarity": 1.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152923703630102580", "type": "ist"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:54", "name": "Uran", "similarity": 0.0}, "metadata": ["outgoing_relation_found_for_subject_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:52", "name": "abgereichertes Uran", "similarity": 1.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152940196304519220", "type": "hat"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:53", "name": "<PERSON><PERSON><PERSON><PERSON>", "similarity": 0.0}, "metadata": ["outgoing_relation_found_for_subject_entity"]}]}]}}, {"test_case_id": "test_004", "claim": "Radioaktive Abfälle werden nicht im Hauptlager eingelagert, sondern bleiben grundsätzlich unverpackt.", "expected_status": "REFUTES", "actual_status": "REFUTES", "explanation": "Die bereitgestellten Fakten zeigen, dass radioaktive Abfälle im Hauptlager eingelagert werden (\"radioaktive Abfälle, werden eingelagert, in Hauptlager\") und dass sie verpackt werden (\"radioaktive Abfälle, werden, verpackt\"). Die Behauptung, dass radioaktive Abfälle nicht im Hauptlager eingelagert werden und grundsätzlich unverpackt bleiben, wird somit durch die Fakten widerlegt.", "correct": true, "extraction": {"original_claim": "Radioaktive Abfälle werden nicht im Hauptlager eingelagert, sondern bleiben grundsätzlich unverpackt.", "extraction_prompt_request": "You are a precise fact extraction system. Your task is to extract atomic facts from a given text.\n\nINSTRUCTIONS:\n- Break down the input text into individual, atomic facts\n- Each fact should be a simple, standalone statement\n- Preserve the original meaning and context\n- Do not add any information not present in the original text\n- Do not make assumptions or inferences beyond what is explicitly stated\n- Each fact should be a complete sentence\n- Avoid redundancy and repetition\n- Extract only factual statements, not opinions or subjective claims\n- Return each fact as a triple in the format (subject, predicate, object)\n- Remove articles (\"the\", \"a\", \"an\", \"das\", \"die\", \"der\", etc.) from the beginning of subjects and objects\n- Keep the core entity names without articles to facilitate entity matching\n- When extracting subjects and objects, prefer the core name or identifier of an entity, and avoid including descriptive prefixes or qualifiers\n- If context is important or clarity may be lost, you may optionally add an additional alias triple in the form: (entity, hat Bezeichnung, full_phrase), where full_phrase is the original compound form from the text.\n- Ensure entity names are clean and directly matchable with knowledge graph entries\n- Always keep the language of the triples in German. Do not translate or change the language of the input or output; the triples must remain in German.\n\n### EXAMPLES ###\n\nINPUT:\n\"Reaktorsicherheitskommission erstellt regelmäßig Berichte über die Anlagensicherheit.\"\n\nOUTPUT:\n(Reaktorsicherheitskommission, erstellt, Berichte über Anlagensicherheit)\n\n---\n\nINPUT:\n\"Primärkühlmittelkreislauf transportiert Wärme von Reaktorkern zu Wärmetauscher.\"\n\nOUTPUT:\n(Primärkühlmittelkreislauf, transportiert, Wärme)\n(Wärme, wird transportiert zu, Wärmetauscher)\n(Wärme, stammt aus, Reaktorkern)\n\n---\n\nINPUT:\n\"Jod-131 hat eine Halbwertszeit von acht Tagen und wird bei Kernspaltungsprozessen freigesetzt.\"\n\nOUTPUT:\n(Jod-131, hat, Halbwertszeit von acht Tagen)\n(Jod-131, wird freigesetzt bei, Kernspaltungsprozesse)\n\n---\n\n### Now process the following input:\n\nINPUT:\nRadioaktive Abfälle werden nicht im Hauptlager eingelagert, sondern bleiben grundsätzlich unverpackt.\n\nOUTPUT:\nList each atomic fact as a triple (subject, predicate, object) on a new line.", "extraction_prompt_result": "(Radioaktive Abfälle, werden nicht eingelagert in, Hauptlager)  \n(Radioaktive Abfälle, bleiben, grundsätzlich unverpackt)", "facts": [{"subject": "Radioaktive Abfälle", "predicate": "werden nicht eingelagert in", "object": "<PERSON><PERSON><PERSON><PERSON>"}, {"subject": "Radioaktive Abfälle", "predicate": "b<PERSON><PERSON>n", "object": "grundsät<PERSON>lich unverpackt"}]}, "retrieval": {"original_claim": "Radioaktive Abfälle werden nicht im Hauptlager eingelagert, sondern bleiben grundsätzlich unverpackt.", "retrievals": [{"fact": {"subject": "Radioaktive Abfälle", "predicate": "werden nicht eingelagert in", "object": "<PERSON><PERSON><PERSON><PERSON>"}, "knowledge": [{"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:835", "name": "<PERSON><PERSON><PERSON><PERSON>", "similarity": 1.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152923703630103363", "type": "ist"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:836", "name": "Gesamtheit aller Lagerfelder", "similarity": 0.0}, "metadata": ["outgoing_relation_found_for_subject_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:835", "name": "<PERSON><PERSON><PERSON><PERSON>", "similarity": 1.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1155175503443788611", "type": "ist"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:839", "name": "Bereich eines geologischen Tiefenlagers", "similarity": 0.0}, "metadata": ["outgoing_relation_found_for_subject_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:838", "name": "Pilotlager", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1153253557118436166", "type": "ist abgetrennt von"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:835", "name": "<PERSON><PERSON><PERSON><PERSON>", "similarity": 1.0}, "metadata": ["incoming_relation_found_for_object_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:37", "name": "radioaktive Abfälle", "similarity": 1.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1153137008885891109", "type": "werden eingelagert"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:840", "name": "in Hauptlager", "similarity": 0.87}, "metadata": ["direct_relation_found_between_subject_and_object"]}]}, {"fact": {"subject": "Radioaktive Abfälle", "predicate": "b<PERSON><PERSON>n", "object": "grundsät<PERSON>lich unverpackt"}, "knowledge": [{"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:37", "name": "radioaktive Abfälle", "similarity": 1.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1153019361141719077", "type": "werden"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1050", "name": "verk<PERSON><PERSON>", "similarity": 0.0}, "metadata": ["outgoing_relation_found_for_subject_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:37", "name": "radioaktive Abfälle", "similarity": 1.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1155271160955404325", "type": "werden"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1051", "name": "dekon<PERSON><PERSON><PERSON>", "similarity": 0.0}, "metadata": ["outgoing_relation_found_for_subject_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:37", "name": "radioaktive Abfälle", "similarity": 1.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1157522960769089573", "type": "werden"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1052", "name": "verp<PERSON><PERSON>", "similarity": 0.0}, "metadata": ["outgoing_relation_found_for_subject_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:37", "name": "radioaktive Abfälle", "similarity": 1.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1159774760582774821", "type": "werden"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1053", "name": "verbrannt", "similarity": 0.0}, "metadata": ["outgoing_relation_found_for_subject_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:37", "name": "radioaktive Abfälle", "similarity": 1.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:6917626884175954975", "type": "werden"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1055", "name": "verp<PERSON>t", "similarity": 0.0}, "metadata": ["outgoing_relation_found_for_subject_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:36", "name": "Abfallprodukt", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152923703630102564", "type": "ist"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:37", "name": "radioaktive Abfälle", "similarity": 1.0}, "metadata": ["incoming_relation_found_for_object_entity"]}]}]}}, {"test_case_id": "test_005", "claim": "Die Abfallmatrix wird regelmäßig von der Europäischen Nuklearsicherheitsagentur evaluiert und nutzt Graphen-basiert<PERSON> Analysen.", "expected_status": "INSUFFICIENT", "actual_status": "INSUFFICIENT", "explanation": "Die bereitgestellten Tripel enthalten Informationen darüber, was eine Abfallmatrix ist, wie sie entsteht und wofür sie verwendet wird (z.B. dass radioaktive Abfälle in eine Abfallmatrix eingebettet werden und dass Bindemittel eine Abfallmatrix erzeugen). Es gibt jedoch keine Informationen darüber, ob die Abfallmatrix regelmäßig von der Europäischen Nuklearsicherheitsagentur evaluiert wird oder ob sie Graphen-basierte Ana<PERSON>sen nutzt. Um den Anspruch zu überprüfen, wären explizite Angaben zu Evaluationsprozessen durch die Europäische Nuklearsicherheitsagentur und zum Einsatz von Graphen-basierten Analysen erforderlich.", "correct": true, "extraction": {"original_claim": "Die Abfallmatrix wird regelmäßig von der Europäischen Nuklearsicherheitsagentur evaluiert und nutzt Graphen-basiert<PERSON> Analysen.", "extraction_prompt_request": "You are a precise fact extraction system. Your task is to extract atomic facts from a given text.\n\nINSTRUCTIONS:\n- Break down the input text into individual, atomic facts\n- Each fact should be a simple, standalone statement\n- Preserve the original meaning and context\n- Do not add any information not present in the original text\n- Do not make assumptions or inferences beyond what is explicitly stated\n- Each fact should be a complete sentence\n- Avoid redundancy and repetition\n- Extract only factual statements, not opinions or subjective claims\n- Return each fact as a triple in the format (subject, predicate, object)\n- Remove articles (\"the\", \"a\", \"an\", \"das\", \"die\", \"der\", etc.) from the beginning of subjects and objects\n- Keep the core entity names without articles to facilitate entity matching\n- When extracting subjects and objects, prefer the core name or identifier of an entity, and avoid including descriptive prefixes or qualifiers\n- If context is important or clarity may be lost, you may optionally add an additional alias triple in the form: (entity, hat Bezeichnung, full_phrase), where full_phrase is the original compound form from the text.\n- Ensure entity names are clean and directly matchable with knowledge graph entries\n- Always keep the language of the triples in German. Do not translate or change the language of the input or output; the triples must remain in German.\n\n### EXAMPLES ###\n\nINPUT:\n\"Reaktorsicherheitskommission erstellt regelmäßig Berichte über die Anlagensicherheit.\"\n\nOUTPUT:\n(Reaktorsicherheitskommission, erstellt, Berichte über Anlagensicherheit)\n\n---\n\nINPUT:\n\"Primärkühlmittelkreislauf transportiert Wärme von Reaktorkern zu Wärmetauscher.\"\n\nOUTPUT:\n(Primärkühlmittelkreislauf, transportiert, Wärme)\n(Wärme, wird transportiert zu, Wärmetauscher)\n(Wärme, stammt aus, Reaktorkern)\n\n---\n\nINPUT:\n\"Jod-131 hat eine Halbwertszeit von acht Tagen und wird bei Kernspaltungsprozessen freigesetzt.\"\n\nOUTPUT:\n(Jod-131, hat, Halbwertszeit von acht Tagen)\n(Jod-131, wird freigesetzt bei, Kernspaltungsprozesse)\n\n---\n\n### Now process the following input:\n\nINPUT:\nDie Abfallmatrix wird regelmäßig von der Europäischen Nuklearsicherheitsagentur evaluiert und nutzt Graphen-basierte Analysen.\n\nOUTPUT:\nList each atomic fact as a triple (subject, predicate, object) on a new line.", "extraction_prompt_result": "(<PERSON><PERSON><PERSON><PERSON><PERSON>, wird evalui<PERSON>, Europäische Nuklearsicherheitsagentur)\n(Abfallmatrix, nutzt, Graphen-basierte Analysen)\n(Europäische Nuklearsicherheitsagentur, hat Bezeichnung, Europäische Nuklearsicherheitsagentur)\n(Graphen-basiert<PERSON>, hat <PERSON>ichnung, Graphen-basiert<PERSON>)", "facts": [{"subject": "Abfallmatrix", "predicate": "wird evaluiert von", "object": "Europäische Nuklearsicherheitsagentur"}, {"subject": "Abfallmatrix", "predicate": "nutzt", "object": "<PERSON><PERSON><PERSON><PERSON>b<PERSON><PERSON><PERSON>"}, {"subject": "Europäische Nuklearsicherheitsagentur", "predicate": "hat Bezeichnung", "object": "Europäische Nuklearsicherheitsagentur"}, {"subject": "<PERSON><PERSON><PERSON><PERSON>b<PERSON><PERSON><PERSON>", "predicate": "hat Bezeichnung", "object": "<PERSON><PERSON><PERSON><PERSON>b<PERSON><PERSON><PERSON>"}]}, "retrieval": {"original_claim": "Die Abfallmatrix wird regelmäßig von der Europäischen Nuklearsicherheitsagentur evaluiert und nutzt Graphen-basiert<PERSON> Analysen.", "retrievals": [{"fact": {"subject": "Abfallmatrix", "predicate": "wird evaluiert von", "object": "Europäische Nuklearsicherheitsagentur"}, "knowledge": [{"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:37", "name": "radioaktive Abfälle", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1153179889839374373", "type": "werden eingebettet in"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1054", "name": "Abfallmatrix", "similarity": 1.0}, "metadata": ["incoming_relation_found_for_object_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:28", "name": "Eine Abfallmatrix", "similarity": 0.83}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152923703630102556", "type": "ist"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:29", "name": "ein spezielles Abfallprodukt", "similarity": 0.0}, "metadata": ["outgoing_relation_found_for_subject_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:40", "name": "Bindemittel", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1153049047955669032", "type": "<PERSON><PERSON><PERSON>"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:417", "name": "eine Abfallmatrix", "similarity": 0.83}, "metadata": ["incoming_relation_found_for_object_entity"]}]}, {"fact": {"subject": "Abfallmatrix", "predicate": "nutzt", "object": "<PERSON><PERSON><PERSON><PERSON>b<PERSON><PERSON><PERSON>"}, "knowledge": [{"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:37", "name": "radioaktive Abfälle", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1153179889839374373", "type": "werden eingebettet in"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1054", "name": "Abfallmatrix", "similarity": 1.0}, "metadata": ["incoming_relation_found_for_object_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:28", "name": "Eine Abfallmatrix", "similarity": 0.83}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152923703630102556", "type": "ist"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:29", "name": "ein spezielles Abfallprodukt", "similarity": 0.0}, "metadata": ["outgoing_relation_found_for_subject_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:40", "name": "Bindemittel", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1153049047955669032", "type": "<PERSON><PERSON><PERSON>"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:417", "name": "eine Abfallmatrix", "similarity": 0.83}, "metadata": ["incoming_relation_found_for_object_entity"]}]}, {"fact": {"subject": "Europäische Nuklearsicherheitsagentur", "predicate": "hat Bezeichnung", "object": "Europäische Nuklearsicherheitsagentur"}, "knowledge": []}, {"fact": {"subject": "<PERSON><PERSON><PERSON><PERSON>b<PERSON><PERSON><PERSON>", "predicate": "hat Bezeichnung", "object": "<PERSON><PERSON><PERSON><PERSON>b<PERSON><PERSON><PERSON>"}, "knowledge": []}]}}, {"test_case_id": "test_006", "claim": "Das Instandsetzungskonzept schreibt vor, dass sämtliche Massnahmen mithil<PERSON> von Augmented-Reality-Brillen dokumentiert werden.", "expected_status": "INSUFFICIENT", "actual_status": "INSUFFICIENT", "explanation": "No knowledge available. We can't verify something without knowledge. Verification prompt is skipped!", "correct": true, "extraction": {"original_claim": "Das Instandsetzungskonzept schreibt vor, dass sämtliche Massnahmen mithil<PERSON> von Augmented-Reality-Brillen dokumentiert werden.", "extraction_prompt_request": "You are a precise fact extraction system. Your task is to extract atomic facts from a given text.\n\nINSTRUCTIONS:\n- Break down the input text into individual, atomic facts\n- Each fact should be a simple, standalone statement\n- Preserve the original meaning and context\n- Do not add any information not present in the original text\n- Do not make assumptions or inferences beyond what is explicitly stated\n- Each fact should be a complete sentence\n- Avoid redundancy and repetition\n- Extract only factual statements, not opinions or subjective claims\n- Return each fact as a triple in the format (subject, predicate, object)\n- Remove articles (\"the\", \"a\", \"an\", \"das\", \"die\", \"der\", etc.) from the beginning of subjects and objects\n- Keep the core entity names without articles to facilitate entity matching\n- When extracting subjects and objects, prefer the core name or identifier of an entity, and avoid including descriptive prefixes or qualifiers\n- If context is important or clarity may be lost, you may optionally add an additional alias triple in the form: (entity, hat Bezeichnung, full_phrase), where full_phrase is the original compound form from the text.\n- Ensure entity names are clean and directly matchable with knowledge graph entries\n- Always keep the language of the triples in German. Do not translate or change the language of the input or output; the triples must remain in German.\n\n### EXAMPLES ###\n\nINPUT:\n\"Reaktorsicherheitskommission erstellt regelmäßig Berichte über die Anlagensicherheit.\"\n\nOUTPUT:\n(Reaktorsicherheitskommission, erstellt, Berichte über Anlagensicherheit)\n\n---\n\nINPUT:\n\"Primärkühlmittelkreislauf transportiert Wärme von Reaktorkern zu Wärmetauscher.\"\n\nOUTPUT:\n(Primärkühlmittelkreislauf, transportiert, Wärme)\n(Wärme, wird transportiert zu, Wärmetauscher)\n(Wärme, stammt aus, Reaktorkern)\n\n---\n\nINPUT:\n\"Jod-131 hat eine Halbwertszeit von acht Tagen und wird bei Kernspaltungsprozessen freigesetzt.\"\n\nOUTPUT:\n(Jod-131, hat, Halbwertszeit von acht Tagen)\n(Jod-131, wird freigesetzt bei, Kernspaltungsprozesse)\n\n---\n\n### Now process the following input:\n\nINPUT:\nDas Instandsetzungskonzept schreibt vor, dass sämtliche Massnahmen mithilfe von Augmented-Reality-Brillen dokumentiert werden.\n\nOUTPUT:\nList each atomic fact as a triple (subject, predicate, object) on a new line.", "extraction_prompt_result": "(Instandsetzungskonzept, sch<PERSON><PERSON><PERSON> vor, sämtliche Massnahmen werden dokumentiert)\n(sämtliche Massnahmen, werden dokumentiert mit, Augmented-Reality-Brillen)\n(Instandsetzungskonzept, hat <PERSON><PERSON>nung, Instandsetzungskonzept)\n(Augmented-Reality-Brillen, hat <PERSON>nung, Augmented-Reality-Brillen)", "facts": [{"subject": "Instandsetzungskonzept", "predicate": "schre<PERSON>t vor", "object": "sämtliche Massnahmen werden dokumentiert"}, {"subject": "sämtliche Massnahmen", "predicate": "werden dokumentiert mit", "object": "Augmented-Reality-<PERSON><PERSON><PERSON>"}, {"subject": "Instandsetzungskonzept", "predicate": "hat Bezeichnung", "object": "Instandsetzungskonzept"}, {"subject": "Augmented-Reality-<PERSON><PERSON><PERSON>", "predicate": "hat Bezeichnung", "object": "Augmented-Reality-<PERSON><PERSON><PERSON>"}]}, "retrieval": {"original_claim": "Das Instandsetzungskonzept schreibt vor, dass sämtliche Massnahmen mithil<PERSON> von Augmented-Reality-Brillen dokumentiert werden.", "retrievals": [{"fact": {"subject": "Instandsetzungskonzept", "predicate": "schre<PERSON>t vor", "object": "sämtliche Massnahmen werden dokumentiert"}, "knowledge": []}, {"fact": {"subject": "sämtliche Massnahmen", "predicate": "werden dokumentiert mit", "object": "Augmented-Reality-<PERSON><PERSON><PERSON>"}, "knowledge": []}, {"fact": {"subject": "Instandsetzungskonzept", "predicate": "hat Bezeichnung", "object": "Instandsetzungskonzept"}, "knowledge": []}, {"fact": {"subject": "Augmented-Reality-<PERSON><PERSON><PERSON>", "predicate": "hat Bezeichnung", "object": "Augmented-Reality-<PERSON><PERSON><PERSON>"}, "knowledge": []}]}}]}, {"strategy_name": "cot", "test_results": [{"test_case_id": "test_001", "claim": "Das Standarddokument \"0E-Alterungsdossier\" enthält sowohl Daten als auch Angaben.", "expected_status": "SUPPORTS", "actual_status": "SUPPORTS", "explanation": "The knowledge states that the \"0E-Alterungsdossier\" contains \"wichtige Daten\" (important data) and also contains \"Angaben zu Wartung\", \"Angabe<PERSON> zu Instandsetzung\", and \"Angaben zu Ersatzteilhaltung\" (information on maintenance, repair, and spare parts management). This supports the claim that the standard document \"0E-Alterungsdossier\" contains both data and information (Angaben).", "correct": true, "extraction": {"original_claim": "Das Standarddokument \"0E-Alterungsdossier\" enthält sowohl Daten als auch Angaben.", "extraction_prompt_request": "You are a precise fact extraction system. Your task is to extract atomic facts from a given German sentence and output them as clean (subject, predicate, object) triples.\n\nINSTRUCTIONS:\n- Break down the input text into individual, atomic facts\n- Each fact should be a simple, standalone statement\n- Preserve the original meaning and context\n- Do not add any information not present in the original text\n- Do not make assumptions or inferences beyond what is explicitly stated\n- Each fact should be a complete sentence\n- Avoid redundancy and repetition\n- Extract only factual statements, not opinions or subjective claims\n- Return each fact as a triple in the format (subject, predicate, object)\n- Remove articles (\"the\", \"a\", \"an\", \"das\", \"die\", \"der\", etc.) from the beginning of subjects and objects\n- Keep the core entity names without articles to facilitate entity matching\n- When extracting subjects and objects, prefer the core name or identifier of an entity, and avoid including descriptive prefixes or qualifiers\n- If context is important or clarity may be lost, you may optionally add an additional alias triple in the form: (entity, hat Bezeichnung, full_phrase), where full_phrase is the original compound form from the text.\n- Ensure entity names are clean and directly matchable with knowledge graph entries\n- Always keep the language of the triples in German. Do not translate or change the language of the input or output; the triples must remain in German.\n\n### EXAMPLE:\n\nINPUT:\n\"Das Notstromaggregat startet automatisch bei Stromausfall und versorgt sicherheitsrelevante Systeme.\"\n\nREASONING:\n- Identify main subject: Notstromaggregat\n- It performs two actions:\n  - startet automatisch bei Stromausfall\n  - versorgt sicherheitsrelevante Systeme\n- Extract each as a separate triple\n- Remove \"Das\" from subject\n- Result: two clean factual triples\n\nOUTPUT:\n(Notstromaggregat, startet bei, Stromausfall)\n(Notstromaggregat, versorgt, sicherheitsrelevante Systeme)\n\n---\n\nINPUT:\nDas Standarddokument \"0E-Alterungsdossier\" enthält sowohl Daten als auch Angaben.\n\nREASONING:\n[Let the model walk through the reasoning here.]\n\nOUTPUT:\n[List the final clean triples here.]", "extraction_prompt_result": "REASONING:\n- Hauptsubjekt: Standarddokument \"0E-Alterungsdossier\"\n- <PERSON>s gibt zwei Aussagen:\n  1. Das Standarddokument enthält Daten.\n  2. Das Standarddokument enthält Angaben.\n- Entferne \"Das\" vom Subjekt.\n- Der Name des Dokuments ist \"0E-Alterungsdossier\", daher kann ein Alias-Triple hinzugefügt werden.\n- Die Aussagen sind atomar und enthalten keine Meinungen.\n\nOUTPUT:\n(Standarddokument \"0E-Alterungsdossier\", enthält, Daten)\n(Standarddokument \"0E-Alterungsdossier\", enthält, Angaben)\n(Standarddokument \"0E-Alterungsdossier\", hat Bezeichnung, 0E-Alterungsdossier)", "facts": [{"subject": "Standarddokument 0E-Alterungsdossier", "predicate": "<PERSON>th<PERSON><PERSON>", "object": "Daten"}, {"subject": "Standarddokument 0E-Alterungsdossier", "predicate": "<PERSON>th<PERSON><PERSON>", "object": "Angaben"}, {"subject": "Standarddokument 0E-Alterungsdossier", "predicate": "hat Bezeichnung", "object": "0E-Alterungsdossier"}]}, "retrieval": {"original_claim": "Das Standarddokument \"0E-Alterungsdossier\" enthält sowohl Daten als auch Angaben.", "retrievals": [{"fact": {"subject": "Standarddokument 0E-Alterungsdossier", "predicate": "<PERSON>th<PERSON><PERSON>", "object": "Daten"}, "knowledge": [{"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1695", "name": "Daten", "similarity": 1.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152922604118476447", "type": "werden benutzt für"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1696", "name": "Beurteilung der Alterung", "similarity": 0.0}, "metadata": ["outgoing_relation_found_for_subject_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:366", "name": "Dokument", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152940196304519534", "type": "hat"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1695", "name": "Daten", "similarity": 1.0}, "metadata": ["incoming_relation_found_for_object_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:191", "name": "<PERSON><PERSON><PERSON><PERSON>", "similarity": 0.77}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152987475304513727", "type": "werden extrahiert aus"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:192", "name": "anlagenspezifische Betriebserfahrung", "similarity": 0.0}, "metadata": ["outgoing_relation_found_for_subject_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:211", "name": "<PERSON>", "similarity": 0.71}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152989674327769299", "type": "werden gesendet an"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:212", "name": "die Aufsichtsbehörde", "similarity": 0.0}, "metadata": ["outgoing_relation_found_for_subject_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1", "name": "wich<PERSON><PERSON> Daten", "similarity": 0.53}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152922604118474753", "type": "werden benutzt für"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:2", "name": "Beurteilung der Alterungsüberwachung", "similarity": 0.0}, "metadata": ["outgoing_relation_found_for_subject_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:0", "name": "0E-Alterungsdossier", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152921504606846976", "type": "<PERSON>th<PERSON><PERSON>"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1", "name": "wich<PERSON><PERSON> Daten", "similarity": 0.53}, "metadata": ["incoming_relation_found_for_object_entity"]}]}, {"fact": {"subject": "Standarddokument 0E-Alterungsdossier", "predicate": "<PERSON>th<PERSON><PERSON>", "object": "Angaben"}, "knowledge": [{"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:509", "name": "Angaben", "similarity": 1.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1153066640141713917", "type": "sind enthalten"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:510", "name": "im Anhang A.3 der IEC 61513", "similarity": 0.0}, "metadata": ["outgoing_relation_found_for_subject_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:577", "name": "Dosismeldung", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1153032555281252929", "type": "bein<PERSON><PERSON>"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:578", "name": "Do<PERSON><PERSON><PERSON>", "similarity": 0.74}, "metadata": ["incoming_relation_found_for_object_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:0", "name": "0E-Alterungsdossier", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1155173304420532224", "type": "<PERSON>th<PERSON><PERSON>"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:3", "name": "<PERSON><PERSON><PERSON>", "similarity": 0.56}, "metadata": ["incoming_relation_found_for_object_entity"]}]}, {"fact": {"subject": "Standarddokument 0E-Alterungsdossier", "predicate": "hat Bezeichnung", "object": "0E-Alterungsdossier"}, "knowledge": [{"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:0", "name": "0E-Alterungsdossier", "similarity": 1.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152921504606846976", "type": "<PERSON>th<PERSON><PERSON>"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1", "name": "wich<PERSON><PERSON> Daten", "similarity": 0.0}, "metadata": ["outgoing_relation_found_for_subject_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:0", "name": "0E-Alterungsdossier", "similarity": 1.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1155173304420532224", "type": "<PERSON>th<PERSON><PERSON>"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:3", "name": "<PERSON><PERSON><PERSON>", "similarity": 0.0}, "metadata": ["outgoing_relation_found_for_subject_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:0", "name": "0E-Alterungsdossier", "similarity": 1.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1157425104234217472", "type": "<PERSON>th<PERSON><PERSON>"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:4", "name": "<PERSON><PERSON><PERSON> zu Instandsetzung", "similarity": 0.0}, "metadata": ["outgoing_relation_found_for_subject_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:0", "name": "0E-Alterungsdossier", "similarity": 1.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1159676904047902720", "type": "<PERSON>th<PERSON><PERSON>"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:5", "name": "<PERSON><PERSON><PERSON> zu Ersatzteilhaltung", "similarity": 0.0}, "metadata": ["outgoing_relation_found_for_subject_entity"]}]}]}}, {"test_case_id": "test_002", "claim": "Die Behörden führen Stichprobenerhebungen und Vergleichsmessungen durch.", "expected_status": "SUPPORTS", "actual_status": "SUPPORTS", "explanation": "The knowledge includes the triple \"(Behörden BAG und ENSI, führen aus, Stichprobenerhebung und Vergleichsmessungen)\" multiple times, which explicitly states that the authorities (BAG and ENSI) conduct sample surveys and comparative measurements. This directly supports the claim that authorities conduct such activities.", "correct": true, "extraction": {"original_claim": "Die Behörden führen Stichprobenerhebungen und Vergleichsmessungen durch.", "extraction_prompt_request": "You are a precise fact extraction system. Your task is to extract atomic facts from a given German sentence and output them as clean (subject, predicate, object) triples.\n\nINSTRUCTIONS:\n- Break down the input text into individual, atomic facts\n- Each fact should be a simple, standalone statement\n- Preserve the original meaning and context\n- Do not add any information not present in the original text\n- Do not make assumptions or inferences beyond what is explicitly stated\n- Each fact should be a complete sentence\n- Avoid redundancy and repetition\n- Extract only factual statements, not opinions or subjective claims\n- Return each fact as a triple in the format (subject, predicate, object)\n- Remove articles (\"the\", \"a\", \"an\", \"das\", \"die\", \"der\", etc.) from the beginning of subjects and objects\n- Keep the core entity names without articles to facilitate entity matching\n- When extracting subjects and objects, prefer the core name or identifier of an entity, and avoid including descriptive prefixes or qualifiers\n- If context is important or clarity may be lost, you may optionally add an additional alias triple in the form: (entity, hat Bezeichnung, full_phrase), where full_phrase is the original compound form from the text.\n- Ensure entity names are clean and directly matchable with knowledge graph entries\n- Always keep the language of the triples in German. Do not translate or change the language of the input or output; the triples must remain in German.\n\n### EXAMPLE:\n\nINPUT:\n\"Das Notstromaggregat startet automatisch bei Stromausfall und versorgt sicherheitsrelevante Systeme.\"\n\nREASONING:\n- Identify main subject: Notstromaggregat\n- It performs two actions:\n  - startet automatisch bei Stromausfall\n  - versorgt sicherheitsrelevante Systeme\n- Extract each as a separate triple\n- Remove \"Das\" from subject\n- Result: two clean factual triples\n\nOUTPUT:\n(Notstromaggregat, startet bei, Stromausfall)\n(Notstromaggregat, versorgt, sicherheitsrelevante Systeme)\n\n---\n\nINPUT:\nDie Behörden führen Stichprobenerhebungen und Vergleichsmessungen durch.\n\nREASONING:\n[Let the model walk through the reasoning here.]\n\nOUTPUT:\n[List the final clean triples here.]", "extraction_prompt_result": "REASONING:\n- Identify main subject: Behörden\n- Two actions are performed:\n  - führen Stichprobenerhebungen durch\n  - führen Vergleichsmessungen durch\n- Remove \"Die\" from subject\n- Each action is a separate atomic fact\n\nOUTPUT:\n(<PERSON><PERSON><PERSON><PERSON>, führen durch, Stichprobenerhebungen)\n(<PERSON><PERSON><PERSON>rden, führen durch, Vergleichsmessungen)", "facts": [{"subject": "Behörden", "predicate": "führen durch", "object": "Stichprobenerhebungen"}, {"subject": "Behörden", "predicate": "führen durch", "object": "Vergleichsmessungen"}]}, "retrieval": {"original_claim": "Die Behörden führen Stichprobenerhebungen und Vergleichsmessungen durch.", "retrievals": [{"fact": {"subject": "Behörden", "predicate": "führen durch", "object": "Stichprobenerhebungen"}, "knowledge": [{"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1682", "name": "Sonderprüfung", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1153315129769592466", "type": "kann verlangt werden von"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1683", "name": "Aufsichtsbehörden", "similarity": 0.64}, "metadata": ["incoming_relation_found_for_object_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:47", "name": "Behörden BAG und ENSI", "similarity": 0.55}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152945693862658095", "type": "führen aus"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:48", "name": "Stichprobenerhebung und Vergleichsmessungen", "similarity": 0.0}, "metadata": ["outgoing_relation_found_for_subject_entity"]}]}, {"fact": {"subject": "Behörden", "predicate": "führen durch", "object": "Vergleichsmessungen"}, "knowledge": [{"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:47", "name": "Behörden BAG und ENSI", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152945693862658095", "type": "führen aus"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:48", "name": "Stichprobenerhebung und Vergleichsmessungen", "similarity": 0.61}, "metadata": ["incoming_relation_found_for_object_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1682", "name": "Sonderprüfung", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1153315129769592466", "type": "kann verlangt werden von"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1683", "name": "Aufsichtsbehörden", "similarity": 0.64}, "metadata": ["incoming_relation_found_for_object_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:47", "name": "Behörden BAG und ENSI", "similarity": 0.55}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152945693862658095", "type": "führen aus"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:48", "name": "Stichprobenerhebung und Vergleichsmessungen", "similarity": 0.61}, "metadata": ["direct_relation_found_between_subject_and_object"]}]}]}}, {"test_case_id": "test_003", "claim": "Abgereichertes Uran besitzt einen höheren Uran-235-<PERSON><PERSON>il als Natururan und wird direkt als Brennstoff verwendet.", "expected_status": "REFUTES", "actual_status": "REFUTES", "explanation": "Die bereitgestellten Informationen besagen, dass abgereichertes Uran einen \"geringeren Anteil\" (implizit an Uran-235) hat. Da Natururan Uran-235 enthält und abgereichertes Uran einen geringeren Anteil davon besitzt, ist die Behauptung, abgereichertes Uran habe einen höheren Uran-235-Anteil als Natururan, falsch. Außerdem gibt es keine Information, dass abgereichertes Uran direkt als Brennstoff verwendet wird. Die Aussage wird somit durch die vorhandenen Fakten widerlegt.", "correct": true, "extraction": {"original_claim": "Abgereichertes Uran besitzt einen höheren Uran-235-<PERSON><PERSON>il als Natururan und wird direkt als Brennstoff verwendet.", "extraction_prompt_request": "You are a precise fact extraction system. Your task is to extract atomic facts from a given German sentence and output them as clean (subject, predicate, object) triples.\n\nINSTRUCTIONS:\n- Break down the input text into individual, atomic facts\n- Each fact should be a simple, standalone statement\n- Preserve the original meaning and context\n- Do not add any information not present in the original text\n- Do not make assumptions or inferences beyond what is explicitly stated\n- Each fact should be a complete sentence\n- Avoid redundancy and repetition\n- Extract only factual statements, not opinions or subjective claims\n- Return each fact as a triple in the format (subject, predicate, object)\n- Remove articles (\"the\", \"a\", \"an\", \"das\", \"die\", \"der\", etc.) from the beginning of subjects and objects\n- Keep the core entity names without articles to facilitate entity matching\n- When extracting subjects and objects, prefer the core name or identifier of an entity, and avoid including descriptive prefixes or qualifiers\n- If context is important or clarity may be lost, you may optionally add an additional alias triple in the form: (entity, hat Bezeichnung, full_phrase), where full_phrase is the original compound form from the text.\n- Ensure entity names are clean and directly matchable with knowledge graph entries\n- Always keep the language of the triples in German. Do not translate or change the language of the input or output; the triples must remain in German.\n\n### EXAMPLE:\n\nINPUT:\n\"Das Notstromaggregat startet automatisch bei Stromausfall und versorgt sicherheitsrelevante Systeme.\"\n\nREASONING:\n- Identify main subject: Notstromaggregat\n- It performs two actions:\n  - startet automatisch bei Stromausfall\n  - versorgt sicherheitsrelevante Systeme\n- Extract each as a separate triple\n- Remove \"Das\" from subject\n- Result: two clean factual triples\n\nOUTPUT:\n(Notstromaggregat, startet bei, Stromausfall)\n(Notstromaggregat, versorgt, sicherheitsrelevante Systeme)\n\n---\n\nINPUT:\nAbgereichertes Uran besitzt einen höheren Uran-235-Anteil als Natururan und wird direkt als Brennstoff verwendet.\n\nREASONING:\n[Let the model walk through the reasoning here.]\n\nOUTPUT:\n[List the final clean triples here.]", "extraction_prompt_result": "REASONING:\n- Hauptsubjekt: abgereichertes Uran\n- Erstes Faktum: besitzt einen höheren Uran-235-Anteil als Natururan\n  - Subjekt: abgereichertes Uran\n  - Prädikat: besitzt höheren Uran-235-<PERSON><PERSON><PERSON> als\n  - Objekt: Natururan\n- Zweites Faktum: wird direkt als Brennstoff verwendet\n  - Subjekt: abgereichertes Uran\n  - Prädikat: wird verwendet als\n  - Objekt: Brennstoff\n- Keine Artikel am Anfang der Subjekte oder Objekte\n- Keine weiteren Aliase notwendig, da die Begriffe eindeutig sind\n\nOUTPUT:\n(abgereichertes Uran, besitzt höheren Uran-235-Anteil als, Natururan)\n(abgereichertes Uran, wird verwendet als, <PERSON><PERSON><PERSON><PERSON>)", "facts": [{"subject": "abgereichertes Uran", "predicate": "besitzt höheren Uran-235-<PERSON><PERSON><PERSON> als", "object": "<PERSON><PERSON><PERSON>"}, {"subject": "abgereichertes Uran", "predicate": "wird verwendet als", "object": "<PERSON><PERSON><PERSON><PERSON>"}]}, "retrieval": {"original_claim": "Abgereichertes Uran besitzt einen höheren Uran-235-<PERSON><PERSON>il als Natururan und wird direkt als Brennstoff verwendet.", "retrievals": [{"fact": {"subject": "abgereichertes Uran", "predicate": "besitzt höheren Uran-235-<PERSON><PERSON><PERSON> als", "object": "<PERSON><PERSON><PERSON>"}, "knowledge": [{"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:56", "name": "<PERSON><PERSON><PERSON>", "similarity": 1.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152923703630102584", "type": "ist"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1282", "name": "Uran mit Isotopenmischung", "similarity": 0.0}, "metadata": ["outgoing_relation_found_for_subject_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:55", "name": "Uran 235", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152948992397541431", "type": "ist <PERSON><PERSON> von"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:56", "name": "<PERSON><PERSON><PERSON>", "similarity": 1.0}, "metadata": ["incoming_relation_found_for_object_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:52", "name": "abgereichertes Uran", "similarity": 1.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152923703630102580", "type": "ist"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:54", "name": "Uran", "similarity": 0.0}, "metadata": ["outgoing_relation_found_for_subject_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:52", "name": "abgereichertes Uran", "similarity": 1.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152940196304519220", "type": "hat"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:53", "name": "<PERSON><PERSON><PERSON><PERSON>", "similarity": 0.0}, "metadata": ["outgoing_relation_found_for_subject_entity"]}]}, {"fact": {"subject": "abgereichertes Uran", "predicate": "wird verwendet als", "object": "<PERSON><PERSON><PERSON><PERSON>"}, "knowledge": [{"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:451", "name": "Brennstab", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1153057844048691651", "type": "ist gefüllt mit"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:453", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "similarity": 0.83}, "metadata": ["incoming_relation_found_for_object_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:2066", "name": "Oxid-<PERSON><PERSON><PERSON><PERSON>", "similarity": 0.8}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152995171885910034", "type": "wird"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:2067", "name": "chemisch aufgelöst", "similarity": 0.0}, "metadata": ["outgoing_relation_found_for_subject_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1554", "name": "<PERSON>sen Folge", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1153060043071948306", "type": "tritt auf"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1555", "name": "ein Brennst<PERSON>", "similarity": 0.61}, "metadata": ["incoming_relation_found_for_object_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:52", "name": "abgereichertes Uran", "similarity": 1.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152923703630102580", "type": "ist"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:54", "name": "Uran", "similarity": 0.0}, "metadata": ["outgoing_relation_found_for_subject_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:52", "name": "abgereichertes Uran", "similarity": 1.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152940196304519220", "type": "hat"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:53", "name": "<PERSON><PERSON><PERSON><PERSON>", "similarity": 0.0}, "metadata": ["outgoing_relation_found_for_subject_entity"]}]}]}}, {"test_case_id": "test_004", "claim": "Radioaktive Abfälle werden nicht im Hauptlager eingelagert, sondern bleiben grundsätzlich unverpackt.", "expected_status": "REFUTES", "actual_status": "REFUTES", "explanation": "Die bereitgestellten Informationen zeigen, dass radioaktive Abfälle im Hauptlager eingelagert werden (\"radioaktive Abfälle, werden eingelagert, in Hauptlager\") und dass sie verpackt werden (\"radioaktive Abfälle, werden, verpackt\"). Die Behauptung, dass radioaktive Abfälle nicht im Hauptlager eingelagert werden und grundsätzlich unverpackt bleiben, wird somit durch die Fakten widerlegt.", "correct": true, "extraction": {"original_claim": "Radioaktive Abfälle werden nicht im Hauptlager eingelagert, sondern bleiben grundsätzlich unverpackt.", "extraction_prompt_request": "You are a precise fact extraction system. Your task is to extract atomic facts from a given German sentence and output them as clean (subject, predicate, object) triples.\n\nINSTRUCTIONS:\n- Break down the input text into individual, atomic facts\n- Each fact should be a simple, standalone statement\n- Preserve the original meaning and context\n- Do not add any information not present in the original text\n- Do not make assumptions or inferences beyond what is explicitly stated\n- Each fact should be a complete sentence\n- Avoid redundancy and repetition\n- Extract only factual statements, not opinions or subjective claims\n- Return each fact as a triple in the format (subject, predicate, object)\n- Remove articles (\"the\", \"a\", \"an\", \"das\", \"die\", \"der\", etc.) from the beginning of subjects and objects\n- Keep the core entity names without articles to facilitate entity matching\n- When extracting subjects and objects, prefer the core name or identifier of an entity, and avoid including descriptive prefixes or qualifiers\n- If context is important or clarity may be lost, you may optionally add an additional alias triple in the form: (entity, hat Bezeichnung, full_phrase), where full_phrase is the original compound form from the text.\n- Ensure entity names are clean and directly matchable with knowledge graph entries\n- Always keep the language of the triples in German. Do not translate or change the language of the input or output; the triples must remain in German.\n\n### EXAMPLE:\n\nINPUT:\n\"Das Notstromaggregat startet automatisch bei Stromausfall und versorgt sicherheitsrelevante Systeme.\"\n\nREASONING:\n- Identify main subject: Notstromaggregat\n- It performs two actions:\n  - startet automatisch bei Stromausfall\n  - versorgt sicherheitsrelevante Systeme\n- Extract each as a separate triple\n- Remove \"Das\" from subject\n- Result: two clean factual triples\n\nOUTPUT:\n(Notstromaggregat, startet bei, Stromausfall)\n(Notstromaggregat, versorgt, sicherheitsrelevante Systeme)\n\n---\n\nINPUT:\nRadioaktive Abfälle werden nicht im Hauptlager eingelagert, sondern bleiben grundsätzlich unverpackt.\n\nREASONING:\n[Let the model walk through the reasoning here.]\n\nOUTPUT:\n[List the final clean triples here.]", "extraction_prompt_result": "REASONING:\n- Hauptthema: radioaktive Abfälle\n- Zwei Aussagen:\n  1. Radioaktive Abfälle werden nicht im Hauptlager eingelagert.\n  2. Radioaktive Abfälle bleiben grundsätzlich unverpackt.\n- Subjekt: radioaktive Abfälle (ohne Artikel)\n- Prädikat und Objekt jeweils klar zu trennen\n- Keine weiteren Entitäten oder Aliase notwendig\n\nOUTPUT:\n(radioaktive Abfälle, werden nicht eingelagert in, Hauptlager)\n(radioaktive Abfälle, bleiben, unverpackt)", "facts": [{"subject": "radioaktive Abfälle", "predicate": "werden nicht eingelagert in", "object": "<PERSON><PERSON><PERSON><PERSON>"}, {"subject": "radioaktive Abfälle", "predicate": "b<PERSON><PERSON>n", "object": "unverpackt"}]}, "retrieval": {"original_claim": "Radioaktive Abfälle werden nicht im Hauptlager eingelagert, sondern bleiben grundsätzlich unverpackt.", "retrievals": [{"fact": {"subject": "radioaktive Abfälle", "predicate": "werden nicht eingelagert in", "object": "<PERSON><PERSON><PERSON><PERSON>"}, "knowledge": [{"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:835", "name": "<PERSON><PERSON><PERSON><PERSON>", "similarity": 1.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152923703630103363", "type": "ist"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:836", "name": "Gesamtheit aller Lagerfelder", "similarity": 0.0}, "metadata": ["outgoing_relation_found_for_subject_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:835", "name": "<PERSON><PERSON><PERSON><PERSON>", "similarity": 1.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1155175503443788611", "type": "ist"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:839", "name": "Bereich eines geologischen Tiefenlagers", "similarity": 0.0}, "metadata": ["outgoing_relation_found_for_subject_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:838", "name": "Pilotlager", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1153253557118436166", "type": "ist abgetrennt von"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:835", "name": "<PERSON><PERSON><PERSON><PERSON>", "similarity": 1.0}, "metadata": ["incoming_relation_found_for_object_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:37", "name": "radioaktive Abfälle", "similarity": 1.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1153137008885891109", "type": "werden eingelagert"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:840", "name": "in Hauptlager", "similarity": 0.87}, "metadata": ["direct_relation_found_between_subject_and_object"]}]}, {"fact": {"subject": "radioaktive Abfälle", "predicate": "b<PERSON><PERSON>n", "object": "unverpackt"}, "knowledge": [{"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:37", "name": "radioaktive Abfälle", "similarity": 1.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1153019361141719077", "type": "werden"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1050", "name": "verk<PERSON><PERSON>", "similarity": 0.0}, "metadata": ["outgoing_relation_found_for_subject_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:37", "name": "radioaktive Abfälle", "similarity": 1.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1155271160955404325", "type": "werden"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1051", "name": "dekon<PERSON><PERSON><PERSON>", "similarity": 0.0}, "metadata": ["outgoing_relation_found_for_subject_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:37", "name": "radioaktive Abfälle", "similarity": 1.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1157522960769089573", "type": "werden"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1052", "name": "verp<PERSON><PERSON>", "similarity": 0.0}, "metadata": ["outgoing_relation_found_for_subject_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:37", "name": "radioaktive Abfälle", "similarity": 1.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1159774760582774821", "type": "werden"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1053", "name": "verbrannt", "similarity": 0.0}, "metadata": ["outgoing_relation_found_for_subject_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:37", "name": "radioaktive Abfälle", "similarity": 1.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:6917626884175954975", "type": "werden"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1055", "name": "verp<PERSON>t", "similarity": 0.0}, "metadata": ["outgoing_relation_found_for_subject_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:36", "name": "Abfallprodukt", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152923703630102564", "type": "ist"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:37", "name": "radioaktive Abfälle", "similarity": 1.0}, "metadata": ["incoming_relation_found_for_object_entity"]}]}]}}, {"test_case_id": "test_005", "claim": "Die Abfallmatrix wird regelmäßig von der Europäischen Nuklearsicherheitsagentur evaluiert und nutzt Graphen-basiert<PERSON> Analysen.", "expected_status": "INSUFFICIENT", "actual_status": "INSUFFICIENT", "explanation": "Die bereitgestellten Tripel enthalten Informationen darüber, was eine Abfallmatrix ist, wie sie entsteht und wofür sie verwendet wird (z.B. Einbettung radioaktiver Abfälle, Erzeugung durch Bindemittel). <PERSON>s gibt jedoch keine <PERSON> darüber, ob die Abfallmatrix regelmäßig von der Europäischen Nuklearsicherheitsagentur evaluiert wird oder ob Graphen-basierte Ana<PERSON>sen dabei genutzt werden. Um den Anspruch zu überprüfen, wären explizite Angaben zu Evaluationsprozessen durch die genannte Agentur und zur Verwendung von Graphen-basierten Ana<PERSON>sen erforderlich.", "correct": true, "extraction": {"original_claim": "Die Abfallmatrix wird regelmäßig von der Europäischen Nuklearsicherheitsagentur evaluiert und nutzt Graphen-basiert<PERSON> Analysen.", "extraction_prompt_request": "You are a precise fact extraction system. Your task is to extract atomic facts from a given German sentence and output them as clean (subject, predicate, object) triples.\n\nINSTRUCTIONS:\n- Break down the input text into individual, atomic facts\n- Each fact should be a simple, standalone statement\n- Preserve the original meaning and context\n- Do not add any information not present in the original text\n- Do not make assumptions or inferences beyond what is explicitly stated\n- Each fact should be a complete sentence\n- Avoid redundancy and repetition\n- Extract only factual statements, not opinions or subjective claims\n- Return each fact as a triple in the format (subject, predicate, object)\n- Remove articles (\"the\", \"a\", \"an\", \"das\", \"die\", \"der\", etc.) from the beginning of subjects and objects\n- Keep the core entity names without articles to facilitate entity matching\n- When extracting subjects and objects, prefer the core name or identifier of an entity, and avoid including descriptive prefixes or qualifiers\n- If context is important or clarity may be lost, you may optionally add an additional alias triple in the form: (entity, hat Bezeichnung, full_phrase), where full_phrase is the original compound form from the text.\n- Ensure entity names are clean and directly matchable with knowledge graph entries\n- Always keep the language of the triples in German. Do not translate or change the language of the input or output; the triples must remain in German.\n\n### EXAMPLE:\n\nINPUT:\n\"Das Notstromaggregat startet automatisch bei Stromausfall und versorgt sicherheitsrelevante Systeme.\"\n\nREASONING:\n- Identify main subject: Notstromaggregat\n- It performs two actions:\n  - startet automatisch bei Stromausfall\n  - versorgt sicherheitsrelevante Systeme\n- Extract each as a separate triple\n- Remove \"Das\" from subject\n- Result: two clean factual triples\n\nOUTPUT:\n(Notstromaggregat, startet bei, Stromausfall)\n(Notstromaggregat, versorgt, sicherheitsrelevante Systeme)\n\n---\n\nINPUT:\nDie Abfallmatrix wird regelmäßig von der Europäischen Nuklearsicherheitsagentur evaluiert und nutzt Graphen-basierte Analysen.\n\nREASONING:\n[Let the model walk through the reasoning here.]\n\nOUTPUT:\n[List the final clean triples here.]", "extraction_prompt_result": "REASONING:\n- Identify main subject: Abfallmatrix (remove \"Die\")\n- Two main predicates:\n  1. wird regel<PERSON><PERSON><PERSON> evaluiert von Europäische Nuklearsicherheitsagentur\n  2. nutzt Graphen-basiert<PERSON>\n- For the first, the agent (Europäische Nuklearsicherheitsagentur) is the evaluator.\n- For the second, <PERSON><PERSON><PERSON><PERSON><PERSON> is the subject, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> is the object.\n- Remove articles from subjects and objects.\n- No need for alias triples, as entities are clear.\n\nOUTPUT:\n(Abfallmatrix, wird evaluiert von, Europäische Nuklearsicherheitsagentur)\n(<PERSON><PERSON>fallmat<PERSON>, nutzt, Graphen-basiert<PERSON>)", "facts": [{"subject": "Abfallmatrix", "predicate": "wird evaluiert von", "object": "Europäische Nuklearsicherheitsagentur"}, {"subject": "Abfallmatrix", "predicate": "nutzt", "object": "<PERSON><PERSON><PERSON><PERSON>b<PERSON><PERSON><PERSON>"}]}, "retrieval": {"original_claim": "Die Abfallmatrix wird regelmäßig von der Europäischen Nuklearsicherheitsagentur evaluiert und nutzt Graphen-basiert<PERSON> Analysen.", "retrievals": [{"fact": {"subject": "Abfallmatrix", "predicate": "wird evaluiert von", "object": "Europäische Nuklearsicherheitsagentur"}, "knowledge": [{"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:37", "name": "radioaktive Abfälle", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1153179889839374373", "type": "werden eingebettet in"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1054", "name": "Abfallmatrix", "similarity": 1.0}, "metadata": ["incoming_relation_found_for_object_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:28", "name": "Eine Abfallmatrix", "similarity": 0.83}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152923703630102556", "type": "ist"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:29", "name": "ein spezielles Abfallprodukt", "similarity": 0.0}, "metadata": ["outgoing_relation_found_for_subject_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:40", "name": "Bindemittel", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1153049047955669032", "type": "<PERSON><PERSON><PERSON>"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:417", "name": "eine Abfallmatrix", "similarity": 0.83}, "metadata": ["incoming_relation_found_for_object_entity"]}]}, {"fact": {"subject": "Abfallmatrix", "predicate": "nutzt", "object": "<PERSON><PERSON><PERSON><PERSON>b<PERSON><PERSON><PERSON>"}, "knowledge": [{"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:37", "name": "radioaktive Abfälle", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1153179889839374373", "type": "werden eingebettet in"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1054", "name": "Abfallmatrix", "similarity": 1.0}, "metadata": ["incoming_relation_found_for_object_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:28", "name": "Eine Abfallmatrix", "similarity": 0.83}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152923703630102556", "type": "ist"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:29", "name": "ein spezielles Abfallprodukt", "similarity": 0.0}, "metadata": ["outgoing_relation_found_for_subject_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:40", "name": "Bindemittel", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1153049047955669032", "type": "<PERSON><PERSON><PERSON>"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:417", "name": "eine Abfallmatrix", "similarity": 0.83}, "metadata": ["incoming_relation_found_for_object_entity"]}]}]}}, {"test_case_id": "test_006", "claim": "Das Instandsetzungskonzept schreibt vor, dass sämtliche Massnahmen mithil<PERSON> von Augmented-Reality-Brillen dokumentiert werden.", "expected_status": "INSUFFICIENT", "actual_status": "INSUFFICIENT", "explanation": "Die bereitgestellten Knowledge-Graph-Tripel enthalten keine Informationen darüber, ob das Instandsetzungskonzept vorschreibt, dass sämtliche Maßnahmen mithilfe von Augmented-Reality-Brillen dokumentiert werden müssen. Es gib<PERSON> zwar <PERSON>el, die beschreiben, dass Instandsetzung Maßnahmen umfasst, aber keine, die auf die Art und Weise der Dokumentation oder den Einsatz von Augmented-Reality-Brillen eingehen. Um den Anspruch zu überprüfen, wären spezifische Informationen zur Dokumentationsvorschrift im Instandsetzungskonzept und zum Einsatz von Augmented-Reality-Brille<PERSON> erford<PERSON>lich.", "correct": true, "extraction": {"original_claim": "Das Instandsetzungskonzept schreibt vor, dass sämtliche Massnahmen mithil<PERSON> von Augmented-Reality-Brillen dokumentiert werden.", "extraction_prompt_request": "You are a precise fact extraction system. Your task is to extract atomic facts from a given German sentence and output them as clean (subject, predicate, object) triples.\n\nINSTRUCTIONS:\n- Break down the input text into individual, atomic facts\n- Each fact should be a simple, standalone statement\n- Preserve the original meaning and context\n- Do not add any information not present in the original text\n- Do not make assumptions or inferences beyond what is explicitly stated\n- Each fact should be a complete sentence\n- Avoid redundancy and repetition\n- Extract only factual statements, not opinions or subjective claims\n- Return each fact as a triple in the format (subject, predicate, object)\n- Remove articles (\"the\", \"a\", \"an\", \"das\", \"die\", \"der\", etc.) from the beginning of subjects and objects\n- Keep the core entity names without articles to facilitate entity matching\n- When extracting subjects and objects, prefer the core name or identifier of an entity, and avoid including descriptive prefixes or qualifiers\n- If context is important or clarity may be lost, you may optionally add an additional alias triple in the form: (entity, hat Bezeichnung, full_phrase), where full_phrase is the original compound form from the text.\n- Ensure entity names are clean and directly matchable with knowledge graph entries\n- Always keep the language of the triples in German. Do not translate or change the language of the input or output; the triples must remain in German.\n\n### EXAMPLE:\n\nINPUT:\n\"Das Notstromaggregat startet automatisch bei Stromausfall und versorgt sicherheitsrelevante Systeme.\"\n\nREASONING:\n- Identify main subject: Notstromaggregat\n- It performs two actions:\n  - startet automatisch bei Stromausfall\n  - versorgt sicherheitsrelevante Systeme\n- Extract each as a separate triple\n- Remove \"Das\" from subject\n- Result: two clean factual triples\n\nOUTPUT:\n(Notstromaggregat, startet bei, Stromausfall)\n(Notstromaggregat, versorgt, sicherheitsrelevante Systeme)\n\n---\n\nINPUT:\nDas Instandsetzungskonzept schreibt vor, dass sämtliche Massnahmen mithilfe von Augmented-Reality-Brillen dokumentiert werden.\n\nREASONING:\n[Let the model walk through the reasoning here.]\n\nOUTPUT:\n[List the final clean triples here.]", "extraction_prompt_result": "REASONING:\n- Hauptsubjekt: Instandsetzungskonzept\n- Das Instandsetzungskonzept schreibt etwas vor (Vorschrift)\n- Was wird vorgeschrieben? Dass sämtliche Massnahmen mithilfe von Augmented-Reality-Brillen dokumentiert werden.\n- <PERSON>aus ergeben sich zwei Fakten:\n  1. Instandsetzungskonzept schreibt vor, dass Massnahmen dokumentiert werden.\n  2. Massnahmen werden mithilfe von Augmented-Reality-Brillen dokumentiert.\n- \"Das\" wird entfernt.\n- \"Augmented-Reality-Brillen\" ist das Mittel, mit dem dokumentiert wird.\n- Optional kann ein Alias für \"Instandsetzungskonzept\" hinzugefügt werden, falls der volle Ausdruck relevant ist.\n\nOUTPUT:\n(Instandsetzungskonzept, schreibt vor, Massnahmen werden dokumentiert)\n(Massnahmen, werden dokumentiert mit, Augmented-Reality-Brillen)", "facts": [{"subject": "Instandsetzungskonzept", "predicate": "schre<PERSON>t vor", "object": "Massnahmen werden dokumentiert"}, {"subject": "Massnahmen", "predicate": "werden dokumentiert mit", "object": "Augmented-Reality-<PERSON><PERSON><PERSON>"}]}, "retrieval": {"original_claim": "Das Instandsetzungskonzept schreibt vor, dass sämtliche Massnahmen mithil<PERSON> von Augmented-Reality-Brillen dokumentiert werden.", "retrievals": [{"fact": {"subject": "Instandsetzungskonzept", "predicate": "schre<PERSON>t vor", "object": "Massnahmen werden dokumentiert"}, "knowledge": []}, {"fact": {"subject": "Massnahmen", "predicate": "werden dokumentiert mit", "object": "Augmented-Reality-<PERSON><PERSON><PERSON>"}, "knowledge": [{"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:120", "name": "Massnahmen", "similarity": 1.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152945693862658168", "type": "führen aus"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:121", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "similarity": 0.0}, "metadata": ["outgoing_relation_found_for_subject_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:120", "name": "Massnahmen", "similarity": 1.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1155197493676343416", "type": "führen aus"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:122", "name": "Beurteilung", "similarity": 0.0}, "metadata": ["outgoing_relation_found_for_subject_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:120", "name": "Massnahmen", "similarity": 1.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1157449293490028664", "type": "führen aus"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:123", "name": "Beherrschung", "similarity": 0.0}, "metadata": ["outgoing_relation_found_for_subject_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:120", "name": "Massnahmen", "similarity": 1.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:6917557614943404210", "type": "beziehen sich auf"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:178", "name": "Systeme", "similarity": 0.0}, "metadata": ["outgoing_relation_found_for_subject_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:120", "name": "Massnahmen", "similarity": 1.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:6917557614943404299", "type": "beziehen sich auf"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:267", "name": "Ausrüstungen", "similarity": 0.0}, "metadata": ["outgoing_relation_found_for_subject_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:119", "name": "Alterungsüberwachung", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152923703630102647", "type": "ist"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:120", "name": "Massnahmen", "similarity": 1.0}, "metadata": ["incoming_relation_found_for_object_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:893", "name": "Instandhaltung", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152923703630103421", "type": "ist"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:120", "name": "Massnahmen", "similarity": 1.0}, "metadata": ["incoming_relation_found_for_object_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:940", "name": "IT-Security", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152923703630103468", "type": "ist"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:120", "name": "Massnahmen", "similarity": 1.0}, "metadata": ["incoming_relation_found_for_object_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:81", "name": "Herstellung", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152927002164985937", "type": "umfasst"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:120", "name": "Massnahmen", "similarity": 1.0}, "metadata": ["incoming_relation_found_for_object_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:82", "name": "Instandsetzung", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152927002164985938", "type": "umfasst"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:120", "name": "Massnahmen", "similarity": 1.0}, "metadata": ["incoming_relation_found_for_object_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:516", "name": "unabhängige leittechnische Einrichtungen", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152985276281258500", "type": "g<PERSON><PERSON><PERSON> zu"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:517", "name": "Handmassnahmen", "similarity": 0.83}, "metadata": ["incoming_relation_found_for_object_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:535", "name": "Die Schutzmassnahmen", "similarity": 0.67}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1153069938676597271", "type": "be<PERSON><PERSON>en"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:536", "name": "Störfälle", "similarity": 0.0}, "metadata": ["outgoing_relation_found_for_subject_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:535", "name": "Die Schutzmassnahmen", "similarity": 0.67}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1153071038188225047", "type": "halten ein"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:537", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> ein", "similarity": 0.0}, "metadata": ["outgoing_relation_found_for_subject_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:648", "name": "Erfolgspfad", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152940196304519816", "type": "hat"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:650", "name": "Sicherheitsmassnahmen", "similarity": 0.65}, "metadata": ["incoming_relation_found_for_object_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:907", "name": "Integrales Überwachungskonzept", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1153153501560308619", "type": "be<PERSON><PERSON><PERSON><PERSON>"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:908", "name": "Überwachungsmassnahmen", "similarity": 0.62}, "metadata": ["incoming_relation_found_for_object_entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:911", "name": "Integrales Überwachungsprogramm", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1153153501560308623", "type": "be<PERSON><PERSON><PERSON><PERSON>"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:908", "name": "Überwachungsmassnahmen", "similarity": 0.62}, "metadata": ["incoming_relation_found_for_object_entity"]}]}]}}]}]}