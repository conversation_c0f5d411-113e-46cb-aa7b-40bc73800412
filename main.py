#!/usr/bin/env python3
"""
FactFence - Main Entry Point

This script runs the complete fact verification pipeline:
1. Extract facts from input claims
2. Retrieve relevant knowledge from Knowledge Graph
3. Verify facts against retrieved knowledge
4. Generate verification report
"""

import os
import sys
from datetime import datetime
from pipeline.pipeline import FactFencePipeline
from utils.io import save_output_data
from utils.logger import initialize_logger
from utils.testdata_loader import load_testdata_yaml
from utils.testdata_types import (
    TestData, StrategyTestResult, TestResults, TestConfiguration
)
from config.config_service import ConfigurationService
from config.config_types import AppConfig, PromptStrategy


def run_pipeline_for_strategy(config: AppConfig, strategy: PromptStrategy, test_data: TestData) -> StrategyTestResult:
    """
    Run pipeline for a single strategy across all test sets.

    Args:
        config: Application configuration
        strategy: Strategy name to use
        test_data: Test data to run

    Returns:
        StrategyTestResults containing all test results for this strategy
    """
    logger = initialize_logger(config)
    logger.info(f"Running pipeline for strategy: {strategy}")
    config.fact_extraction.strategy = strategy

    pipeline = FactFencePipeline(config)
    test_results = pipeline.verify_claims(test_data.test_cases)
    strategy_results = StrategyTestResult(
        strategy_name=strategy,
        test_results=test_results
    )

    return strategy_results


def main():
    config_service = ConfigurationService()
    config = config_service.setup()
    logger = initialize_logger(config)

    # Load test data from YAML
    logger.info(f"Loading test data from: {config.input.testdata_file}")
    try:
        test_data = load_testdata_yaml(config.input.testdata_file)
        logger.info(f"Loaded {len(test_data.test_cases)} test cases")
    except Exception as e:
        logger.error(f"Failed to load test data: {str(e)}")
        return 1

    # Get strategies to run
    logger.info(f"Running pipeline with strategies: {config.fact_extraction.strategies}")

    # Run pipeline for each strategy
    multi_strategy_results = TestResults(
        strategy_results=[],
        configuration=TestConfiguration(
            retrieval=config.fuzzy_retrieval
        )
    )

    for strategy in config.fact_extraction.strategies:
        try:
            strategy_results = run_pipeline_for_strategy(config, strategy, test_data)
            multi_strategy_results.strategy_results.append(strategy_results)
        except Exception as e:
            logger.error(f"Failed to run strategy {strategy}: {str(e)}")
            continue

    # Save results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = os.path.join(
        config.output.directory, f"multi_strategy_results_{timestamp}.json"
    )

    logger.info(f"Saving results to: {results_file}")

    save_output_data(multi_strategy_results, results_file)

    return 0


if __name__ == "__main__":
    sys.exit(main())
