"""
Knowledge Graph Factory - Creates the appropriate knowledge graph service

This module provides a factory for creating knowledge graph services
based on the configuration.
"""

import logging
from typing import Union

from kg.neo4j_service import Neo4jService
from config.config_types import KnowledgeGraphConfig

logger = logging.getLogger(__name__)


class KnowledgeGraphFactory:
    """
    Factory for creating knowledge graph services.
    """

    @staticmethod
    def create_service(
        config: KnowledgeGraphConfig,
    ) -> Union[Neo4jService]:
        """
        Create a knowledge graph service based on the configuration.

        Args:
            config: Knowledge graph configuration

        Returns:
            Knowledge graph service instance
        """
        kg_type = config.type.lower()

        if kg_type == "neo4j":
            logger.info("Creating Neo4j knowledge graph service")
            return Neo4jService(config)
        else:
            logger.error(f"Unknown knowledge graph type: {kg_type}")
            raise ValueError(f"Unknown knowledge graph type: {kg_type}")
